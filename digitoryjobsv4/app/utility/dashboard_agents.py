"""
Dashboard Agents - LLM-powered dashboard generation with business intelligence
"""
from typing import List

import pandas as pd



# ===== COLOR CONFIGURATION =====
GLOBAL_CHART_COLORS = [
    '#ff8c42',  # Orange primary
    '#ffb366',  # Orange light
    '#87a96b',  # <PERSON> green
    '#6b9bd2',  # Soft blue
    '#9b7bb8',  # Muted purple
    '#8d7b68',  # Warm gray
    '#d4a5a5',  # <PERSON> rose
    '#ffc999',  # Orange lighter
    '#a4c085',  # Sage green light
    '#85aedb',  # Soft blue light
    '#af95c6',  # Muted purple light
    '#a69082',  # Warm gray light
    '#ddb8b8',  # Dusty rose light
    '#ffe0cc',  # Orange lightest
    '#f4a261'   # Warning orange
]

SEMANTIC_COLORS = {
    'inward': GLOBAL_CHART_COLORS[3],     # Soft blue
    'outward': GLOBAL_CHART_COLORS[6],    # <PERSON> rose
    'neutral': GLOBAL_CHART_COLORS[5],    # Warm gray
    'positive': GLOBAL_CHART_COLORS[2],   # <PERSON> green
    'negative': GLOBAL_CHART_COLORS[6],   # Dusty rose
    'warning': GLOBAL_CHART_COLORS[14],   # Warning orange
    'primary': GLOBAL_CHART_COLORS[0],    # Orange primary
    'secondary': GLOBAL_CHART_COLORS[1],  # Orange light
    'accent': GLOBAL_CHART_COLORS[2],     # Sage green
    'info': GLOBAL_CHART_COLORS[3],       # Soft blue
    'purchase': GLOBAL_CHART_COLORS[0],   # Orange primary
    'inventory': GLOBAL_CHART_COLORS[2],  # Sage green
    'sales': GLOBAL_CHART_COLORS[2],      # Sage green
    'spoilage': GLOBAL_CHART_COLORS[6],   # Dusty rose
    'transfer': GLOBAL_CHART_COLORS[5],   # Warm gray
    'success': GLOBAL_CHART_COLORS[2],    # Sage green (alias for positive)
    'danger': GLOBAL_CHART_COLORS[6]      # Dusty rose (alias for negative)
}

def get_global_chart_colors() -> List[str]:
    """Orange-based professional color palette matching frontend configuration"""
    return GLOBAL_CHART_COLORS.copy()

def get_semantic_colors() -> dict:
    """Semantic color mappings for consistent chart styling"""
    return SEMANTIC_COLORS.copy()

# ===== DATA MODELS =====


# ===== HELPER FUNCTIONS =====

def format_indian_currency(amount):
    """Format currency in Indian decimal system (1,50,000 instead of 150,000)"""
    if amount == 0:
        return "0"

    # Handle negative numbers
    is_negative = amount < 0
    amount = abs(amount)

    # Convert to string and split by decimal point
    amount_str = f"{amount:.0f}"

    # Handle Indian number system formatting
    if len(amount_str) <= 3:
        formatted = amount_str
    elif len(amount_str) <= 5:
        formatted = amount_str[:-3] + ',' + amount_str[-3:]
    elif len(amount_str) <= 7:
        formatted = amount_str[:-5] + ',' + amount_str[-5:-3] + ',' + amount_str[-3:]
    elif len(amount_str) <= 9:
        formatted = amount_str[:-7] + ',' + amount_str[-7:-5] + ',' + amount_str[-5:-3] + ',' + amount_str[-3:]
    else:
        # For very large numbers, continue the pattern
        formatted = amount_str
        # Add commas from right to left: first after 3 digits, then every 2 digits
        if len(formatted) > 3:
            formatted = formatted[:-3] + ',' + formatted[-3:]
        pos = len(formatted) - 6  # Start position for next comma (skip the first comma and 3 digits)
        while pos > 0:
            formatted = formatted[:pos] + ',' + formatted[pos:]
            pos -= 2

    return ('-' if is_negative else '') + formatted
def create_reconciliation_table_data(store_df: pd.DataFrame, consumption_df: pd.DataFrame, category_workarea_mappings: list = None) -> dict:
    """Create reconciliation table data with proper inventory reconciliation formulas.

    RECONCILIATION FORMULA: Opening Stock + Purchases + Transfers In - Transfers Out - Closing Stock = Consumption

    Args:
        store_df: Store variance dataframe
        consumption_df: Inventory consumption dataframe
        category_workarea_mappings: Category-workarea mappings for cross-category indent calculations
    """

    # Initialize the reconciliation table structure
    reconciliation_table = {}

    # Get all unique categories and subcategories from both dataframes
    all_categories = set()
    all_subcategories_by_category = {}

    # Create mapping lookup for filtering categories
    mapped_categories = set()
    category_to_workareas = {}

    if category_workarea_mappings:
        for mapping in category_workarea_mappings:
            category_name = mapping.get('categoryName', '').strip()
            work_areas = mapping.get('workAreas', [])
            if category_name and work_areas:
                mapped_categories.add(category_name)
                category_to_workareas[category_name] = work_areas

    # Extract categories and subcategories from store_df
    if not store_df.empty:
        for _, row in store_df.iterrows():
            category = str(row.get('Category', 'Others')).strip()
            subcategory = str(row.get('Sub Category', 'Uncategorized')).strip()

            if category and category != 'nan' and category != 'None':
                # Filter by category-workarea mappings if provided
                if category_workarea_mappings and mapped_categories:
                    if category not in mapped_categories:
                        continue

                all_categories.add(category)
                if category not in all_subcategories_by_category:
                    all_subcategories_by_category[category] = set()
                if subcategory and subcategory != 'nan' and subcategory != 'None':
                    all_subcategories_by_category[category].add(subcategory)

    # Extract categories and subcategories from consumption_df
    if not consumption_df.empty:
        for _, row in consumption_df.iterrows():
            category = str(row.get('Category', 'Others')).strip()
            subcategory = str(row.get('Sub Category', 'Uncategorized')).strip()

            if category and category != 'nan' and category != 'None':
                # Filter by category-workarea mappings if provided
                if category_workarea_mappings and mapped_categories:
                    if category not in mapped_categories:
                        continue

                all_categories.add(category)
                if category not in all_subcategories_by_category:
                    all_subcategories_by_category[category] = set()
                if subcategory and subcategory != 'nan' and subcategory != 'None':
                    all_subcategories_by_category[category].add(subcategory)

    # Initialize reconciliation data structure for all found categories and subcategories
    for category in sorted(all_categories):
        reconciliation_table[category] = {
            'subcategories': {},
            'totals': {
                'opening_stock_store': 0,
                'opening_stock_kitchen': 0,
                'opening_stock_total': 0,
                'purchase': 0,
                'transfer_in_out_store': 0,
                'transfer_in_out_kitchen': 0,
                'closing_stock_store': 0,
                'closing_stock_kitchen': 0,
                'closing_stock_total': 0,
                'consumption': 0
            }
        }

        # Initialize all subcategories for this category
        if category in all_subcategories_by_category:
            for subcategory in sorted(all_subcategories_by_category[category]):
                reconciliation_table[category]['subcategories'][subcategory] = {
                    'opening_stock_store': 0,
                    'opening_stock_kitchen': 0,
                    'opening_stock_total': 0,
                    'purchase': 0,
                    'transfer_in_out_store': 0,
                    'transfer_in_out_kitchen': 0,
                    'closing_stock_store': 0,
                    'closing_stock_kitchen': 0,
                    'closing_stock_total': 0,
                    'consumption': 0,
                    'transfer_details': {
                        'indent': 0,
                        'transfer_in': 0,
                        'transfer_out': 0,
                        'return_to_store': 0,
                        'spoilage_adjustments': 0,
                        'cross_category_indents': 0
                    }
                }

    # Process store variance data dynamically
    if not store_df.empty:
        for _, row in store_df.iterrows():
            category = str(row.get('Category', 'Others')).strip()
            subcategory = str(row.get('Sub Category', 'Uncategorized')).strip()
            location = str(row.get('Location', 'Store')).lower().strip()

            # Skip invalid entries
            if not category or category == 'nan' or category == 'None':
                continue
            if not subcategory or subcategory == 'nan' or subcategory == 'None':
                continue
            if category not in reconciliation_table:
                continue
            if subcategory not in reconciliation_table[category]['subcategories']:
                continue

            # Extract values with proper error handling
            opening_amount = 0
            purchase_amount = 0
            closing_amount = 0

            # Store transfer components
            ibt_in_amount = 0
            ibt_out_amount = 0
            return_qty_amount = 0
            spoilage_amount = 0
            indent_amount = 0

            try:
                opening_amount = float(row.get('Opening Amount', 0) or 0)
                purchase_amount = float(row.get('Purchase Amount', 0) or 0)
                closing_amount = float(row.get('Closing Amount', 0) or 0)

                # Store transfer components
                ibt_in_amount = float(row.get('Ibt In Amount', 0) or 0)
                ibt_out_amount = float(row.get('Ibt Out Amount', 0) or 0)
                return_qty_amount = float(row.get('Return-Qty Amount', 0) or 0)
                spoilage_amount = float(row.get('Spoilage Amount', 0) or 0)
                indent_amount = float(row.get('Indent Amount', 0) or 0)
            except (ValueError, TypeError):
                # Handle cases where values can't be converted to float
                pass

            # Map location to store/kitchen based on actual location data
            is_kitchen = 'kitchen' in location
            is_bar = 'bar' in location
            is_store = 'store' in location

            subcat_data = reconciliation_table[category]['subcategories'][subcategory]

            # Assign amounts based on location
            if is_kitchen:
                subcat_data['opening_stock_kitchen'] += opening_amount
                subcat_data['closing_stock_kitchen'] += closing_amount
            elif is_bar:
                # Bar can be treated as store or separate - based on your business logic
                subcat_data['opening_stock_store'] += opening_amount
                subcat_data['closing_stock_store'] += closing_amount
            elif is_store:
                subcat_data['opening_stock_store'] += opening_amount
                subcat_data['closing_stock_store'] += closing_amount
            else:
                # Default to store if location is unclear
                subcat_data['opening_stock_store'] += opening_amount
                subcat_data['closing_stock_store'] += closing_amount

            # Add purchase amount
            subcat_data['purchase'] += purchase_amount

            # Calculate store transfers: Purchase + IBT In - IBT Out - Returns + Spoilage
            # Purchase is the primary inflow, IBT In is additional inflow
            # Returns are typically negative (money back), so we subtract them
            # NOTE: Indent is NOT included here to avoid double-counting with kitchen transfers
            store_transfer_in = purchase_amount + ibt_in_amount  # Purchase and IBT In are inward
            store_transfer_out = ibt_out_amount + abs(return_qty_amount)  # IBT Out and Returns are outward
            store_net_transfer = store_transfer_in - store_transfer_out + spoilage_amount

            subcat_data['transfer_in_out_store'] += store_net_transfer

            # Store detailed store transfer breakdown for UI display
            if 'store_transfer_details' not in subcat_data:
                subcat_data['store_transfer_details'] = {
                    'purchase': 0,
                    'ibt_in': 0,
                    'ibt_out': 0,
                    'return_qty': 0,
                    'spoilage': 0,
                    'indent': 0
                }

            # Add to store transfer details for UI breakdown
            subcat_data['store_transfer_details']['purchase'] += purchase_amount
            subcat_data['store_transfer_details']['ibt_in'] += ibt_in_amount
            subcat_data['store_transfer_details']['ibt_out'] += ibt_out_amount
            subcat_data['store_transfer_details']['return_qty'] += return_qty_amount
            subcat_data['store_transfer_details']['spoilage'] += spoilage_amount
            subcat_data['store_transfer_details']['indent'] += indent_amount

            # Calculate totals
            subcat_data['opening_stock_total'] = subcat_data['opening_stock_store'] + subcat_data['opening_stock_kitchen']
            subcat_data['closing_stock_total'] = subcat_data['closing_stock_store'] + subcat_data['closing_stock_kitchen']

    # Process consumption data dynamically - INCLUDES WORKAREA OPENING/CLOSING STOCK
    if not consumption_df.empty:

        for _, row in consumption_df.iterrows():
            category = str(row.get('Category', 'Others')).strip()
            subcategory = str(row.get('Sub Category', 'Uncategorized')).strip()
            workarea = str(row.get('WorkArea', '')).strip()

            # Skip invalid entries
            if not category or category == 'nan' or category == 'None':
                continue
            if not subcategory or subcategory == 'nan' or subcategory == 'None':
                continue
            if category not in reconciliation_table:
                continue
            if subcategory not in reconciliation_table[category]['subcategories']:
                continue

            # Filter by category-workarea mappings if provided
            if category_workarea_mappings and mapped_categories:
                if category not in mapped_categories:
                    continue
                # Check if this workarea is mapped to this category
                if workarea and category in category_to_workareas:
                    if workarea not in category_to_workareas[category]:
                        continue

            # Extract kitchen stock and transfer values (consumption will be calculated using reconciliation formula)
            kitchen_opening_value = 0
            kitchen_closing_value = 0

            # Transfer calculations based on your specifications
            transfer_in_value = 0  # WorkArea Transfer In (positive/inward) - INDENT EXCLUDED
            transfer_out_value = 0  # WorkArea Transfer Out + Return To Store Out (negative/outward)
            spoilage_adjustment_value = 0  # Can be positive or negative

            try:
                # Get WAC (unit price) first for quantity to value conversions
                unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)

                # Extract workarea opening and closing stock values (already in value format)
                kitchen_opening_value = float(row.get('WorkArea Opening(incl.tax,etc)', 0) or 0)
                kitchen_closing_value = float(row.get('WorkArea Closing(incl.tax,etc)', 0) or 0)

                # Extract transfer quantities and convert to values by multiplying with WAC
                # INWARD (Positive): WorkArea Transfer In ONLY - INDENT EXCLUDED
                workarea_transfer_in_qty = float(row.get('WorkArea Transfer In', 0) or 0)
                transfer_in_value = workarea_transfer_in_qty * unit_price if unit_price > 0 else 0

                # OUTWARD (Negative): WorkArea Transfer Out + Return To Store Out
                workarea_transfer_out_qty = float(row.get('WorkArea Transfer Out', 0) or 0)
                return_to_store_out_qty = float(row.get('Return To Store Out', 0) or 0)

                # Convert quantities to values
                workarea_transfer_out_value = workarea_transfer_out_qty * unit_price if unit_price > 0 else 0
                return_to_store_out_value = return_to_store_out_qty * unit_price if unit_price > 0 else 0
                transfer_out_value = workarea_transfer_out_value + return_to_store_out_value

                # SPOILAGE/ADJUSTMENTS: Can be positive or negative (quantity, convert to value)
                spoilage_adjustment_qty = float(row.get('Spoilage/Adjustments', 0) or 0)
                spoilage_adjustment_value = spoilage_adjustment_qty * unit_price if unit_price > 0 else 0

            except (ValueError, TypeError):
                pass

            subcat_data = reconciliation_table[category]['subcategories'][subcategory]

            # Add kitchen opening and closing stock
            subcat_data['opening_stock_kitchen'] += kitchen_opening_value
            subcat_data['closing_stock_kitchen'] += kitchen_closing_value

            # Kitchen Net Transfer = Transfer In - Transfer Out - Return To Store + Spoilage/Adjustments
            kitchen_net_transfer = transfer_in_value - transfer_out_value + spoilage_adjustment_value
            subcat_data['transfer_in_out_kitchen'] += kitchen_net_transfer

            # Store transfer breakdown details for UI
            subcat_data['transfer_details']['transfer_in'] += transfer_in_value
            subcat_data['transfer_details']['transfer_out'] += workarea_transfer_out_value
            subcat_data['transfer_details']['return_to_store'] += return_to_store_out_value
            subcat_data['transfer_details']['spoilage_adjustments'] += spoilage_adjustment_value

            # Update totals
            subcat_data['opening_stock_total'] = subcat_data['opening_stock_store'] + subcat_data['opening_stock_kitchen']
            subcat_data['closing_stock_total'] = subcat_data['closing_stock_store'] + subcat_data['closing_stock_kitchen']

    # Calculate category totals and consumption using reconciliation formula
    for category in reconciliation_table:
        for subcategory, subcategory_data in reconciliation_table[category]['subcategories'].items():
            # Aggregate category totals
            reconciliation_table[category]['totals']['opening_stock_store'] += subcategory_data['opening_stock_store']
            reconciliation_table[category]['totals']['opening_stock_kitchen'] += subcategory_data['opening_stock_kitchen']
            reconciliation_table[category]['totals']['opening_stock_total'] += subcategory_data['opening_stock_total']
            reconciliation_table[category]['totals']['purchase'] += subcategory_data['purchase']
            reconciliation_table[category]['totals']['transfer_in_out_store'] += subcategory_data['transfer_in_out_store']
            reconciliation_table[category]['totals']['transfer_in_out_kitchen'] += subcategory_data['transfer_in_out_kitchen']
            reconciliation_table[category]['totals']['closing_stock_store'] += subcategory_data['closing_stock_store']
            reconciliation_table[category]['totals']['closing_stock_kitchen'] += subcategory_data['closing_stock_kitchen']
            reconciliation_table[category]['totals']['closing_stock_total'] += subcategory_data['closing_stock_total']

            # RECONCILIATION FORMULA: Opening + Store Transfer + Kitchen Transfer - Closing = Consumption
            calculated_consumption = (
                subcategory_data['opening_stock_total'] +
                subcategory_data['transfer_in_out_store'] +
                subcategory_data['transfer_in_out_kitchen'] -
                subcategory_data['closing_stock_total']
            )

            subcategory_data['consumption'] = calculated_consumption
            reconciliation_table[category]['totals']['consumption'] += calculated_consumption

    # Calculate cross-category indents (bidirectional zero-sum system)
    if category_workarea_mappings and mapped_categories and not consumption_df.empty:
        indent_data = {}  # {workarea: {category: {subcategory: indent_value}}}

        for _, row in consumption_df.iterrows():
            category = str(row.get('Category', 'Others')).strip()
            subcategory = str(row.get('Sub Category', 'Uncategorized')).strip()
            workarea = str(row.get('WorkArea', '')).strip()

            if (not category or category == 'nan' or category == 'None' or
                not subcategory or subcategory == 'nan' or subcategory == 'None' or
                not workarea or category not in reconciliation_table or
                subcategory not in reconciliation_table[category]['subcategories']):
                continue

            try:
                unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
                indent_qty = float(row.get('WorkArea Indent', 0) or 0)
                indent_value = indent_qty * unit_price if unit_price > 0 else 0

                if abs(indent_value) > 0.01:
                    if workarea not in indent_data:
                        indent_data[workarea] = {}
                    if category not in indent_data[workarea]:
                        indent_data[workarea][category] = {}
                    if subcategory not in indent_data[workarea][category]:
                        indent_data[workarea][category][subcategory] = 0
                    indent_data[workarea][category][subcategory] += indent_value

            except (ValueError, TypeError):
                continue

        # Process cross-category flows
        for workarea, workarea_categories in indent_data.items():
            workarea_owner_category = None
            for category_name, work_areas in category_to_workareas.items():
                if workarea in work_areas:
                    workarea_owner_category = category_name
                    break

            if not workarea_owner_category:
                continue

            for indent_category, subcategories in workarea_categories.items():
                for subcategory, indent_value in subcategories.items():
                    if indent_category == workarea_owner_category:
                        continue  # Same category, not cross-category

                    # Cross-category indent: indent_category items consumed in workarea_owner_category workarea

                    # Negative for giving category
                    if (indent_category in reconciliation_table and
                        subcategory in reconciliation_table[indent_category]['subcategories']):
                        reconciliation_table[indent_category]['subcategories'][subcategory]['transfer_details']['cross_category_indents'] -= indent_value

                    # Positive for receiving category - create special subcategory
                    if workarea_owner_category in reconciliation_table:
                        special_subcat_name = "Goods from Other Categories' Indents"

                        if special_subcat_name not in reconciliation_table[workarea_owner_category]['subcategories']:
                            reconciliation_table[workarea_owner_category]['subcategories'][special_subcat_name] = {
                                'opening_stock_store': 0, 'opening_stock_kitchen': 0, 'opening_stock_total': 0,
                                'purchase': 0, 'transfer_in_out_store': 0, 'transfer_in_out_kitchen': 0,
                                'closing_stock_store': 0, 'closing_stock_kitchen': 0, 'closing_stock_total': 0,
                                'consumption': 0,
                                'transfer_details': {
                                    'indent': 0, 'transfer_in': 0, 'transfer_out': 0, 'return_to_store': 0,
                                    'spoilage_adjustments': 0, 'cross_category_indents': 0
                                }
                            }

                        reconciliation_table[workarea_owner_category]['subcategories'][special_subcat_name]['transfer_details']['cross_category_indents'] += indent_value

    # Update totals to include cross-category indents
    for category, category_data in reconciliation_table.items():
        category_cross_category_total = 0

        for subcategory, subcat_data in category_data['subcategories'].items():
            if 'transfer_details' in subcat_data:
                cross_category_indent = subcat_data['transfer_details'].get('cross_category_indents', 0)
                subcat_data['transfer_in_out_kitchen'] += cross_category_indent
                category_cross_category_total += cross_category_indent

        category_data['totals']['transfer_in_out_kitchen'] += category_cross_category_total

        # Recalculate consumption for affected subcategories
        for subcategory, subcat_data in category_data['subcategories'].items():
            if 'transfer_details' in subcat_data and subcat_data['transfer_details'].get('cross_category_indents', 0) != 0:
                calculated_consumption = (
                    subcat_data['opening_stock_total'] +
                    subcat_data['transfer_in_out_store'] +
                    subcat_data['transfer_in_out_kitchen'] -
                    subcat_data['closing_stock_total']
                )

                old_consumption = subcat_data['consumption']
                subcat_data['consumption'] = calculated_consumption
                category_data['totals']['consumption'] += (calculated_consumption - old_consumption)

    return reconciliation_table




def process_department_sales_data(sales_data: dict) -> dict:
    """Process department-wise sales data from RMS API"""
    processed_sales = {}

    if not sales_data:
        return processed_sales

    try:
        # Extract department sales from the API response
        # The data structure has departmentData array with sales information
        department_data = sales_data.get('departmentData', [])

        if department_data and isinstance(department_data, list):
            for dept_item in department_data:
                # Skip header row (style: 0) and total row (style: 2)
                if dept_item.get('style') == 1:  # Only process data rows
                    dept_name = dept_item.get('label', 'Unknown Department')

                    # Parse sales amount (remove commas and convert to float)
                    amount_str = dept_item.get('amount', '0.00')
                    try:
                        # Remove commas and convert to float
                        dept_sales = float(amount_str.replace(',', ''))
                    except (ValueError, AttributeError):
                        dept_sales = 0.0

                    # Parse quantity
                    quantity_str = dept_item.get('numberOfSales', '0')
                    try:
                        dept_quantity = float(quantity_str.replace(',', ''))
                    except (ValueError, AttributeError):
                        dept_quantity = 0.0

                    # Include all departments, even with zero sales
                    processed_sales[dept_name] = {
                        'sales_amount': dept_sales,
                        'sales_quantity': dept_quantity,
                        'formatted_sales': f"₹{dept_sales:,.0f}"
                    }

        return processed_sales

    except Exception:
        return processed_sales


def generate_food_cost_analysis_table(reconciliation_data: dict, processed_sales: dict, category_to_department: dict) -> dict:
    """Generate food cost analysis table showing sales vs consumption breakdown by department -> category -> subcategory"""

    # Group data by department first
    department_structure = {}
    total_sales_all_departments = 0

    # Build department structure with their sales and mapped categories
    if category_to_department:
        # Create department -> categories mapping
        dept_to_categories = {}
        for category, dept_info in category_to_department.items():
            dept_name = dept_info['name']
            if dept_name not in dept_to_categories:
                dept_to_categories[dept_name] = []
            dept_to_categories[dept_name].append(category)

        # For each department, get sales data and calculate consumption
        for dept_name, categories in dept_to_categories.items():
            # Find sales data for this department with improved matching
            dept_sales = 0

            # Try exact match first
            if dept_name in processed_sales:
                dept_sales = processed_sales[dept_name]['sales_amount']
            else:
                # Try fuzzy matching with improved logic
                for sales_dept_name, sales_info in processed_sales.items():
                    # Clean both names for comparison
                    clean_dept_name = dept_name.lower().strip()
                    clean_sales_name = sales_dept_name.lower().strip()

                    if (clean_dept_name == clean_sales_name or
                        clean_dept_name in clean_sales_name or
                        clean_sales_name in clean_dept_name or
                        # Handle common variations
                        clean_dept_name.replace(' ', '') == clean_sales_name.replace(' ', '') or
                        clean_dept_name.replace('department', '').strip() == clean_sales_name.replace('department', '').strip()):
                        dept_sales = sales_info['sales_amount']
                        break

            # Include departments even if sales data is missing (to show consumption)
            # Always add to total sales if dept_sales > 0
            if dept_sales > 0:
                total_sales_all_departments += dept_sales

            # Get categories and their consumption for this department
            dept_categories = []
            dept_total_consumption = 0

            for category in categories:
                if category in reconciliation_data:
                    category_data = reconciliation_data[category]
                    category_consumption = category_data['totals']['consumption']

                    if category_consumption > 0:
                        # Get subcategories for this category
                        subcategories = []
                        for subcat_name, subcat_data in category_data['subcategories'].items():
                            subcat_consumption = subcat_data.get('consumption', 0)
                            if subcat_consumption > 0:
                                subcategories.append({
                                    'name': subcat_name,
                                    'consumption': subcat_consumption
                                })

                        dept_categories.append({
                            'name': category,
                            'consumption': category_consumption,
                            'subcategories': subcategories
                        })
                        dept_total_consumption += category_consumption

            # Add department if it has categories with consumption OR if it has sales data
            if dept_categories or dept_sales > 0:
                department_structure[dept_name] = {
                    'sales': dept_sales,
                    'total_consumption': dept_total_consumption,
                    'categories': dept_categories
                }



    # Calculate total consumption across all departments
    total_consumption_all_depts = sum(dept_data['total_consumption'] for dept_data in department_structure.values())

    # Build the food cost analysis table with department hierarchy
    table_data = []

    # Add Total Sales row first for easy validation
    table_data.append({
        "Department": "OVERALL",
        "Category": "TOTAL SALES",
        "Subcategory": "",
        "Amount (₹)": format_indian_currency(total_sales_all_departments),
        "% of Sales": "100.00",
        "% of Total": "",
        "_isOverallSales": True,
        "_overallSalesRow": True,
        "_rowType": "overallSales"
    })

    # Add Total Consumption row immediately after for easy validation
    table_data.append({
        "Department": "OVERALL",
        "Category": "TOTAL CONSUMPTION",
        "Subcategory": "",
        "Amount (₹)": format_indian_currency(total_consumption_all_depts),
        "% of Sales": f"{(total_consumption_all_depts / total_sales_all_departments * 100) if total_sales_all_departments > 0 else 0:.2f}",
        "% of Total": f"{(total_consumption_all_depts / total_consumption_all_depts * 100) if total_consumption_all_depts > 0 else 0:.2f}",
        "_isOverallConsumption": True,
        "_overallConsumptionRow": True,
        "_rowType": "overallConsumption"
    })

    # Add separator row
    table_data.append({
        "Department": "",
        "Category": "--- DEPARTMENT BREAKDOWN ---",
        "Subcategory": "",
        "Amount (₹)": "",
        "% of Sales": "",
        "% of Total": "",
        "_isSeparator": True,
        "_separatorRow": True,
        "_rowType": "separator"
    })

    # Process each department with column-based hierarchy
    for dept_name, dept_data in department_structure.items():
        dept_sales = dept_data['sales']
        dept_consumption = dept_data['total_consumption']
        dept_cost_percentage = (dept_consumption / dept_sales * 100) if dept_sales > 0 else 0

        # Department sales header row
        table_data.append({
            "Department": f"{dept_name.upper()}",
            "Category": "SALES",
            "Subcategory": "",
            "Amount (₹)": format_indian_currency(dept_sales),
            "% of Sales": "100.00",
            "% of Total": "",
            "_isDepartmentSales": True,
            "_departmentSalesRow": True,
            "_rowType": "departmentSales"
        })

        # Department consumption total row - immediately after sales for easy comparison
        table_data.append({
            "Department": f"{dept_name.upper()}",
            "Category": "TOTAL CONSUMPTION",
            "Subcategory": "",
            "Amount (₹)": format_indian_currency(dept_consumption),
            "% of Sales": f"{dept_cost_percentage:.2f}",
            "% of Total": "100.00",
            "_isDepartmentTotal": True,
            "_departmentTotalRow": True,
            "_rowType": "departmentTotal"
        })

        # Add categories for this department
        for category in dept_data['categories']:
            category_consumption = category['consumption']
            category_sales_percentage = (category_consumption / dept_sales * 100) if dept_sales > 0 else 0
            category_total_percentage = (category_consumption / dept_consumption * 100) if dept_consumption > 0 else 0

            # Check if category has subcategories
            if category['subcategories']:
                # Category header row (when it has subcategories)
                table_data.append({
                    "Department": dept_name,
                    "Category": f"{category['name']}",
                    "Subcategory": "TOTAL",
                    "Amount (₹)": format_indian_currency(category_consumption),
                    "% of Sales": f"{category_sales_percentage:.2f}",
                    "% of Total": f"{category_total_percentage:.2f}",
                    "_isCategory": True,
                    "_categoryRow": True,
                    "_rowType": "category"
                })

                # Add subcategories
                for subcategory in category['subcategories']:
                    subcat_consumption = subcategory['consumption']
                    subcat_sales_percentage = (subcat_consumption / dept_sales * 100) if dept_sales > 0 else 0
                    subcat_total_percentage = (subcat_consumption / dept_consumption * 100) if dept_consumption > 0 else 0



                    table_data.append({
                        "Department": dept_name,
                        "Category": category['name'],
                        "Subcategory": f"{subcategory['name']}",
                        "Amount (₹)": format_indian_currency(subcat_consumption),
                        "% of Sales": f"{subcat_sales_percentage:.2f}",
                        "% of Total": f"{subcat_total_percentage:.2f}",
                        "_isSubcategoryRow": True,
                        "_subcategoryRow": True,
                        "_rowType": "subcategory"
                    })
            else:
                # Category without subcategories
                table_data.append({
                    "Department": dept_name,
                    "Category": f"{category['name']}",
                    "Subcategory": "",
                    "Amount (₹)": format_indian_currency(category_consumption),
                    "% of Sales": f"{category_sales_percentage:.2f}",
                    "% of Total": f"{category_total_percentage:.2f}",
                    "_isCategory": True,
                    "_categoryRow": True,
                    "_rowType": "category"
                })

        # Add department divider row after each department for better visual separation
        table_data.append({
            "Department": "",
            "Category": "",
            "Subcategory": "",
            "Amount (₹)": "",
            "% of Sales": "",
            "% of Total": "",
            "_isDepartmentDivider": True,
            "_departmentDividerRow": True,
            "_rowType": "departmentDivider"
        })







    return {
        "id": "food_cost_analysis",
        "title": "📋 Food Cost Analysis: Department Sales vs Consumption Breakdown",
        "subtitle": f"Detailed analysis showing {len(department_structure)} department(s) with cost percentages (Cost % = Consumption ÷ Sales × 100)",
        "type": "table",
        "size": "full",
        "data": {
            "headers": ["Department", "Category", "Subcategory", "Amount (₹)", "% of Sales", "% of Total"],
            "rows": table_data
        },
        "options": {
            "responsive": True,
            "pageSize": 100,
            "searchable": True,
            "sortable": False,
            "exportable": True,
            "showPagination": True,
            "showSearch": True,
            "showExport": True,
            "columnWidths": {
                "Department": "18%",
                "Category": "25%",
                "Subcategory": "22%",
                "Amount": "15%",
                "% of Sales": "10%",
                "% of Total": "10%"
            },
            "styling": {
                "headerBackground": "#ff9800",
                "headerColor": "#ffffff",
                "headerFontWeight": "600",
                "headerFontSize": "12px",
                "alternateRowColor": "#fff3e0",
                "borderColor": "#e0e0e0",
                "departmentSalesRowBackground": "#e8f5e8",
                "departmentSalesRowColor": "#1b5e20",
                "departmentSalesRowFontWeight": "700",
                "categoryRowBackground": "#ffe0b2",
                "categoryRowColor": "#f57c00",
                "categoryRowFontWeight": "600",
                "subcategoryRowBackground": "#ffffff",
                "subcategoryRowColor": "#424242",
                "subcategoryRowFontWeight": "400",
                "departmentTotalRowBackground": "#ffecb3",
                "departmentTotalRowColor": "#e65100",
                "departmentTotalRowFontWeight": "700",
                "overallSalesRowBackground": "#e8f5e8",
                "overallSalesRowColor": "#1b5e20",
                "overallSalesRowFontWeight": "700",
                "overallConsumptionRowBackground": "#ffecb3",
                "overallConsumptionRowColor": "#e65100",
                "overallConsumptionRowFontWeight": "700",
                "separatorRowBackground": "#f5f5f5",
                "separatorRowColor": "#757575",
                "separatorRowFontWeight": "600",
                "departmentDividerRowBackground": "#ffffff",
                "departmentDividerRowColor": "#ffffff",
                "departmentDividerRowHeight": "20px",
                "overallTotalRowBackground": "#f3e5f5",
                "overallTotalRowColor": "#4a148c",
                "overallTotalRowFontWeight": "700",
                "fontSize": "11px",
                "fontFamily": "Inter, Segoe UI, Tahoma, Geneva, Verdana, sans-serif",
                "lineHeight": "1.3",
                "padding": "10px 8px",
                "compactMode": True,
                "multiColumnHierarchy": True,
                "borderSpacing": "0",
                "tableLayout": "fixed",
                "wordWrap": "break-word"
            }
        }
    }

# ===== DASHBOARD GENERATORS =====
def generate_purchase_dashboard(df: pd.DataFrame) -> dict:
    """Generate comprehensive Purchase Department dashboard with advanced analytics and insights"""
    try:
        colors = get_global_chart_colors()
        semantic_colors = get_semantic_colors()
        charts = []
        summary_items = []

        if df.empty:
            return {
                "success": False,
                "charts": [],
                "summary_items": []
            }

        # === COMPREHENSIVE PURCHASE ANALYTICS LEVERAGING FULL GRN DATAFRAME ===

        # Row 1: Core Financial Metrics
        if 'Total(incl.tax,etc)' in df.columns:
            total_cost = df['Total(incl.tax,etc)'].sum()
            summary_items.append({
                "icon": "currency_rupee",
                "value": f"{total_cost:,.2f}",
                "label": "Total Purchase Value (₹)",
                "data_type": "currency"
            })

        if 'Total(excl.tax)' in df.columns:
            total_excl_tax = df['Total(excl.tax)'].sum()
            summary_items.append({
                "icon": "receipt_long",
                "value": f"{total_excl_tax:,.2f}",
                "label": "Purchase Value Excl. Tax (₹)",
                "data_type": "currency"
            })

        if 'Tax Amount' in df.columns:
            total_tax = df['Tax Amount'].sum()
            tax_percentage = (total_tax / total_excl_tax * 100) if total_excl_tax > 0 else 0
            summary_items.append({
                "icon": "account_balance",
                "value": f"{total_tax:,.2f} ({tax_percentage:.1f})",
                "label": "Total Tax Amount (₹) (%)",
                "data_type": "currency"
            })

        if 'Transportation' in df.columns:
            total_transport = df['Transportation'].sum()
            summary_items.append({
                "icon": "local_shipping",
                "value": f"{total_transport:,.2f}",
                "label": "Transportation Charges (₹)",
                "data_type": "currency"
            })

        # Row 2: Quantity & Volume Metrics
        if 'Return Qty' in df.columns and 'Received Qty' in df.columns:
            total_items = df['Received Qty'].sum()
            total_returns = df['Return Qty'].sum()
            return_rate = (total_returns / total_items * 100) if total_items > 0 else 0
            summary_items.append({
                "icon": "keyboard_return",
                "value": f"{total_returns:,.0f} ({return_rate:.1f})",
                "label": "Return Quantities (%)",
                "data_type": "number"
            })

        # Row 3: Vendor & Purchase Order Analytics
        if 'Vendor Id' in df.columns:
            unique_vendors = df['Vendor Id'].nunique()
            summary_items.append({
                "icon": "store",
                "value": f"{unique_vendors:,}",
                "label": "Active Vendors",
                "data_type": "number"
            })

        if 'GRN Id' in df.columns:
            unique_grns = df['GRN Id'].nunique()
            summary_items.append({
                "icon": "receipt",
                "value": f"{unique_grns:,}",
                "label": "Total GRN Records",
                "data_type": "number"
            })

        if 'Location' in df.columns:
            unique_locations = df['Location'].nunique()
            summary_items.append({
                "icon": "location_on",
                "value": f"{unique_locations:,}",
                "label": "Purchase Locations",
                "data_type": "number"
            })

        # Row 4: Cost Efficiency & Quality Metrics
        if 'Discount' in df.columns:
            total_discount = df['Discount'].sum()
            discount_percentage = (total_discount / total_cost * 100) if total_cost > 0 else 0
            summary_items.append({
                "icon": "local_offer",
                "value": f"{total_discount:,.2f} ({discount_percentage:.1f})",
                "label": "Total Discounts Received (₹) (%)",
                "data_type": "currency"
            })

        if 'Extra Charges' in df.columns:
            total_extra = df['Extra Charges'].sum()
            summary_items.append({
                "icon": "add_circle",
                "value": f"{total_extra:,.2f}",
                "label": "Extra Charges (₹)",
                "data_type": "currency"
            })

        # Row 5: Item Diversity
        if 'Item Code' in df.columns:
            unique_items = df['Item Code'].nunique()
            summary_items.append({
                "icon": "inventory",
                "value": f"{unique_items:,}",
                "label": "Unique Items Purchased",
                "data_type": "number"
            })

        # Row 6: Operational Efficiency & Compliance
        # Calculate average processing time if date columns are available
        if all(col in df.columns for col in ['GRN Date', 'Invoice Date']):
            try:
                df_dates = df.copy()
                df_dates['GRN Date'] = pd.to_datetime(df_dates['GRN Date'], errors='coerce')
                df_dates['Invoice Date'] = pd.to_datetime(df_dates['Invoice Date'], errors='coerce')
                df_dates = df_dates.dropna(subset=['GRN Date', 'Invoice Date'])

                if not df_dates.empty:
                    df_dates['processing_days'] = (df_dates['GRN Date'] - df_dates['Invoice Date']).dt.days
                    avg_processing_days = df_dates['processing_days'].mean()
                    summary_items.append({
                        "icon": "schedule",
                        "value": f"{avg_processing_days:.1f}",
                        "label": "Avg Processing Time (days)",
                        "data_type": "number"
                    })
            except:
                pass

        # Quality & Compliance Metrics
        if 'HSN/SAC' in df.columns:
            hsn_compliance = len(df[df['HSN/SAC'].notna() & (df['HSN/SAC'] != '-')])
            hsn_rate = (hsn_compliance / len(df) * 100) if len(df) > 0 else 0
            summary_items.append({
                "icon": "verified_user",
                "value": f"{hsn_rate:.1f}",
                "label": "HSN/SAC Compliance (%)",
                "data_type": "percentage"
            })

        # === ENHANCED CHARTS WITH ADVANCED BUSINESS INTELLIGENCE ===
        # Charts ordered by business importance: Financial → Operational → Strategic

        # 1. MOST CRITICAL: Purchase Trends Analysis - Daily & Weekly Patterns
        date_columns = [col for col in df.columns if any(word in col.lower() for word in ['date', 'entry'])]
        if date_columns and 'Total(incl.tax,etc)' in df.columns:
            date_col = date_columns[0]
            try:
                df_copy = df.copy()
                if df_copy[date_col].dtype == 'object':
                    df_copy[date_col] = pd.to_datetime(df_copy[date_col], errors='coerce')

                # Daily trends for detailed analysis
                df_copy['Date'] = df_copy[date_col].dt.date
                daily_trends = df_copy.groupby('Date').agg({
                    'Total(incl.tax,etc)': 'sum',
                    'Order Qty': 'sum',
                    'Received Qty': 'sum',
                    'PO Id': 'nunique',
                    'Vendor Name': 'nunique'
                }).round(2)

                # Weekly trends for pattern recognition
                df_copy['Week'] = df_copy[date_col].dt.to_period('W')
                weekly_trends = df_copy.groupby('Week').agg({
                    'Total(incl.tax,etc)': 'sum',
                    'Order Qty': 'sum',
                    'Received Qty': 'sum',
                    'PO Id': 'nunique',
                    'Vendor Name': 'nunique'
                }).round(2)

                # Use daily if we have reasonable amount of data, otherwise weekly
                if len(daily_trends) > 1 and len(daily_trends) <= 30:
                    # Daily trends
                    trend_labels = [str(date) for date in daily_trends.index]
                    trend_values = [float(val) for val in daily_trends['Total(incl.tax,etc)'].tolist()]
                    order_qtys = [int(val) for val in daily_trends['Order Qty'].tolist()]
                    received_qtys = [int(val) for val in daily_trends['Received Qty'].tolist()]
                    po_counts = [int(val) for val in daily_trends['PO Id'].tolist()]
                    vendor_counts = [int(val) for val in daily_trends['Vendor Name'].tolist()]
                    trend_title = "📈 Daily Purchase Trends - Financial Performance"
                    trend_subtitle = "Critical: Monitor daily spending patterns and cash flow impact"
                    x_axis_title = "Date"
                elif len(weekly_trends) > 1:
                    # Weekly trends
                    trend_labels = [str(week) for week in weekly_trends.index]
                    trend_values = [float(val) for val in weekly_trends['Total(incl.tax,etc)'].tolist()]
                    order_qtys = [int(val) for val in weekly_trends['Order Qty'].tolist()]
                    received_qtys = [int(val) for val in weekly_trends['Received Qty'].tolist()]
                    po_counts = [int(val) for val in weekly_trends['PO Id'].tolist()]
                    vendor_counts = [int(val) for val in weekly_trends['Vendor Name'].tolist()]
                    trend_title = "📈 Weekly Purchase Trends - Financial Performance"
                    trend_subtitle = "Critical: Monitor weekly spending patterns and seasonal cycles"
                    x_axis_title = "Week"
                else:
                    trend_labels = []
                    trend_values = []

                if trend_labels and trend_values:
                    # Professional light blue gradient
                    light_blue = "rgba(59, 130, 246, 0.8)"  # Professional blue
                    light_blue_bg = "rgba(59, 130, 246, 0.1)"  # Very light background

                    charts.append({
                        "id": "purchase_trends_analysis",
                        "title": trend_title,
                        "subtitle": trend_subtitle,
                        "type": "line",
                        "size": "full",
                        "data": {
                            "labels": trend_labels,
                            "datasets": [{
                                "label": "Purchase Value (₹)",
                                "data": trend_values,
                                "backgroundColor": light_blue_bg,
                                "borderColor": light_blue,
                                "borderWidth": 3,
                                "fill": True,
                                "tension": 0.3,
                                "pointBackgroundColor": light_blue,
                                "pointBorderColor": '#FFFFFF',
                                "pointBorderWidth": 2,
                                "pointRadius": 5,
                                "pointHoverRadius": 8
                            }]
                        },
                        "tooltipData": {
                            "orderQtys": order_qtys,
                            "receivedQtys": received_qtys,
                            "poCounts": po_counts,
                            "vendorCounts": vendor_counts,
                            "avgValue": sum(trend_values) / len(trend_values) if trend_values else 0
                        },
                        "options": {
                            "responsive": True,
                            "maintainAspectRatio": False,
                            "plugins": {
                                "legend": {"display": True},
                                "tooltip": {
                                    "mode": "index",
                                    "intersect": False,
                                    "backgroundColor": "rgba(255, 255, 255, 0.95)",
                                    "titleColor": "#1f2937",
                                    "bodyColor": "#374151",
                                    "borderColor": light_blue,
                                    "borderWidth": 1,
                                    "displayColors": False,
                                    "bodySpacing": 6,
                                    "titleSpacing": 6,
                                    "footerSpacing": 6,
                                    "cornerRadius": 8,
                                    "caretPadding": 10
                                }
                            },
                            "scales": {
                                "x": {
                                    "title": {
                                        "display": True,
                                        "text": x_axis_title,
                                        "color": "#6b7280"
                                    },
                                    "grid": {"display": False},
                                    "ticks": {"color": "#6b7280"}
                                },
                                "y": {
                                    "beginAtZero": True,
                                    "title": {
                                        "display": True,
                                        "text": "Purchase Value (₹)",
                                        "color": "#6b7280"
                                    },
                                    "grid": {"color": "rgba(156, 163, 175, 0.2)"},
                                    "ticks": {
                                        "color": "#6b7280",
                                        "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                    }
                                }
                            }
                        }
                    })
            except Exception:
                pass

        # 2. CRITICAL: Vendor Performance Matrix - Multi-dimensional Analysis
        if all(col in df.columns for col in ['Vendor Name', 'Total(incl.tax,etc)', 'PO Id']):
            vendor_analysis = df.groupby('Vendor Name').agg({
                'Total(incl.tax,etc)': 'sum',
                'PO Id': 'nunique'
            }).round(2)

            vendor_analysis['Avg_Order_Value'] = vendor_analysis.apply(
                lambda row: row['Total(incl.tax,etc)'] / row['PO Id'] if row['PO Id'] > 0 else 0, axis=1
            )

            # Get top 8 vendors by purchase value for focused analysis
            top_vendors = vendor_analysis.nlargest(8, 'Total(incl.tax,etc)')

            if not top_vendors.empty:
                vendor_names = [name[:15] + '...' if len(name) > 15 else name for name in top_vendors.index.tolist()]
                purchase_values = [float(val) for val in top_vendors['Total(incl.tax,etc)'].tolist()]

                # Professional gradient colors - lighter and more elegant
                professional_colors = [
                    "rgba(99, 102, 241, 0.7)",   # Indigo
                    "rgba(59, 130, 246, 0.7)",   # Blue
                    "rgba(16, 185, 129, 0.7)",   # Emerald
                    "rgba(245, 158, 11, 0.7)",   # Amber
                    "rgba(239, 68, 68, 0.7)",    # Red
                    "rgba(139, 92, 246, 0.7)",   # Violet
                    "rgba(236, 72, 153, 0.7)",   # Pink
                    "rgba(34, 197, 94, 0.7)"     # Green
                ]

                charts.append({
                    "id": "vendor_performance_matrix",
                    "title": "🎯 Top Vendors by Purchase Value - Strategic Partnership Focus",
                    "subtitle": "Critical: Identify your highest-value vendor relationships",
                    "type": "bar",
                    "size": "half",
                    "data": {
                        "labels": vendor_names,
                        "datasets": [{
                            "label": "Purchase Value (₹)",
                            "data": purchase_values,
                            "backgroundColor": professional_colors[:len(top_vendors)],
                            "borderColor": [color.replace('0.7', '1.0') for color in professional_colors[:len(top_vendors)]],
                            "borderWidth": 1,
                            "borderRadius": 8
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {"display": False},
                            "tooltip": {
                                "backgroundColor": "rgba(255, 255, 255, 0.95)",
                                "titleColor": "#1f2937",
                                "bodyColor": "#374151",
                                "borderColor": "rgba(99, 102, 241, 0.7)",
                                "borderWidth": 1,
                                "callbacks": {
                                    "title": f"function(context) {{ return {str(top_vendors.index.tolist())}[context[0].dataIndex]; }}",
                                    "label": f"function(context) {{ var avgOrderValues = {str([float(val) for val in top_vendors['Avg_Order_Value'].tolist()])}; var poCount = {str([int(val) for val in top_vendors['PO Id'].tolist()])}; return ['💰 Purchase Value: ₹' + context.parsed.y.toLocaleString(), '📊 Avg Order Value: ₹' + avgOrderValues[context.dataIndex].toLocaleString(), '📋 Total POs: ' + poCount[context.dataIndex]]; }}",
                                    "footer": "function(context) { return '💼 Strategic vendor - Focus on relationship management'; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"color": "#6b7280", "font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "rgba(156, 163, 175, 0.2)"},
                                "title": {
                                    "display": True,
                                    "text": "Purchase Value (₹)",
                                    "color": "#6b7280",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "color": "#6b7280",
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 3. FINANCIAL: Location-wise Purchase Analysis - Cost Distribution
        if 'Location' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            location_analysis = df.groupby('Location').agg({
                'Total(incl.tax,etc)': 'sum',
                'Order Qty': 'sum',
                'Received Qty': 'sum',
                'Vendor Name': 'nunique'
            }).round(2)

            location_analysis['Cost_per_Unit'] = location_analysis.apply(
                lambda row: row['Total(incl.tax,etc)'] / row['Received Qty'] if row['Received Qty'] > 0 else 0, axis=1
            )
            location_analysis['Vendor_Diversity'] = location_analysis['Vendor Name']

            location_costs = location_analysis.sort_values('Total(incl.tax,etc)', ascending=False)

            if not location_costs.empty:
                charts.append({
                    "id": "location_purchase_analysis",
                    "title": "📍 Location-wise Purchase Analysis - Cost & Efficiency",
                    "subtitle": "Strategic: Compare purchasing efficiency across locations",
                    "type": "bar",
                    "size": "half",
                    "data": {
                        "labels": location_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": [float(val) for val in location_costs['Total(incl.tax,etc)'].tolist()],
                            "backgroundColor": colors[:len(location_costs)],
                            "borderColor": colors[:len(location_costs)],
                            "borderWidth": 1,
                            "borderRadius": 4
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "tooltip": {
                                "callbacks": {
                                    "label": f"function(context) {{ var costPerUnit = {str([float(val) for val in location_costs['Cost_per_Unit'].tolist()])}; var vendorCount = {str([int(val) for val in location_costs['Vendor_Diversity'].tolist()])}; return ['Purchase Cost: ₹' + context.parsed.y.toLocaleString(), 'Cost per Unit: ₹' + costPerUnit[context.dataIndex].toFixed(2), 'Vendor Count: ' + vendorCount[context.dataIndex]]; }}"
                                }
                            }
                        }
                    }
                })

        # 3. OPERATIONAL: Category-wise Purchase Distribution with Trend Analysis
        if 'Category' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            category_costs = df.groupby('Category')['Total(incl.tax,etc)'].sum().sort_values(ascending=False)
            if not category_costs.empty:
                charts.append({
                    "id": "category_purchase_distribution",
                    "title": "🏷️ Category-wise Purchase Distribution",
                    "subtitle": "Operational: Understand spending patterns across product categories",
                    "type": "doughnut",
                    "size": "half",
                    "data": {
                        "labels": category_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": category_costs.values.tolist(),
                            "backgroundColor": colors[:len(category_costs)],
                            "borderColor": '#FFFFFF',
                            "borderWidth": 2
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "right",
                                "labels": {"usePointStyle": True, "padding": 20}
                            }
                        }
                    }
                })

        # 4. OPERATIONAL: High-Value Items Analysis - Focus on Cost Impact
        if 'Item Name' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            item_analysis = df.groupby('Item Name').agg({
                'Total(incl.tax,etc)': 'sum',
                'Order Qty': 'sum',
                'Received Qty': 'sum',
                'Vendor Name': 'nunique'
            }).round(2)

            item_analysis['Avg_Unit_Cost'] = item_analysis.apply(
                lambda row: row['Total(incl.tax,etc)'] / row['Received Qty'] if row['Received Qty'] > 0 else 0, axis=1
            )
            top_items = item_analysis.nlargest(12, 'Total(incl.tax,etc)')

            if not top_items.empty:
                item_names = [name[:20] + '...' if len(name) > 20 else name for name in top_items.index.tolist()]

                charts.append({
                    "id": "high_value_items_analysis",
                    "title": "💰 High-Value Items Analysis - Cost Impact Focus",
                    "subtitle": "Operational: Monitor your highest-cost items for better cost control",
                    "type": "bar",
                    "size": "half",
                    "data": {
                        "labels": item_names,
                        "datasets": [{
                            "label": "Total Purchase Value (₹)",
                            "data": [float(val) for val in top_items['Total(incl.tax,etc)'].tolist()],
                            "backgroundColor": colors[:len(top_items)],
                            "borderColor": colors[:len(top_items)],
                            "borderWidth": 1,
                            "borderRadius": 4
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "tooltip": {
                                "callbacks": {
                                    "label": f"function(context) {{ var avgCosts = {str([float(val) for val in top_items['Avg_Unit_Cost'].tolist()])}; var vendorCounts = {str([int(val) for val in top_items['Vendor Name'].tolist()])}; return ['Total Value: ₹' + context.parsed.y.toLocaleString(), 'Avg Unit Cost: ₹' + avgCosts[context.dataIndex].toFixed(2), 'Vendor Options: ' + vendorCounts[context.dataIndex]]; }}"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "ticks": {
                                    "maxRotation": 45,
                                    "font": {"size": 9}
                                }
                            },
                            "y": {
                                "beginAtZero": True,
                                "ticks": {
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 7. FINANCIAL: Tax Analysis - Optimize Tax Efficiency
        if all(col in df.columns for col in ['Tax(%)', 'Tax Amount', 'Total(excl.tax)', 'Category']):
            tax_analysis = df.groupby('Category').agg({
                'Tax Amount': 'sum',
                'Total(excl.tax)': 'sum',
                'Total(incl.tax,etc)': 'sum'
            }).round(2)

            tax_analysis['Effective_Tax_Rate'] = tax_analysis.apply(
                lambda row: (row['Tax Amount'] / row['Total(excl.tax)'] * 100) if row['Total(excl.tax)'] > 0 else 0, axis=1
            )
            tax_categories = tax_analysis.nlargest(8, 'Tax Amount')

            if not tax_categories.empty:
                charts.append({
                    "id": "tax_efficiency_analysis",
                    "title": "💼 Tax Efficiency Analysis by Category",
                    "subtitle": "Financial: Monitor tax rates and optimize tax efficiency",
                    "type": "bar",
                    "size": "full",
                    "data": {
                        "labels": tax_categories.index.tolist(),
                        "datasets": [
                            {
                                "label": "Tax Amount (₹)",
                                "data": [float(val) for val in tax_categories['Tax Amount'].tolist()],
                                "backgroundColor": semantic_colors['info'],
                                "borderColor": semantic_colors['info'],
                                "borderWidth": 1,
                                "borderRadius": 4,
                                "yAxisID": 'y'
                            },
                            {
                                "label": "Effective Tax Rate (%)",
                                "data": [float(val) for val in tax_categories['Effective_Tax_Rate'].tolist()],
                                "backgroundColor": semantic_colors['warning'],
                                "borderColor": semantic_colors['warning'],
                                "borderWidth": 1,
                                "borderRadius": 4,
                                "yAxisID": 'y1',
                                "type": 'line'
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {"usePointStyle": True, "padding": 20}
                            }
                        },
                        "scales": {
                            "y": {
                                "type": "linear",
                                "display": True,
                                "position": "left",
                                "title": {"display": True, "text": "Tax Amount (₹)"}
                            },
                            "y1": {
                                "type": "linear",
                                "display": True,
                                "position": "right",
                                "title": {"display": True, "text": "Tax Rate (%)"},
                                "grid": {"drawOnChartArea": False}
                            }
                        }
                    }
                })



        return {"success": True, "charts": charts, "summary_items": summary_items}

    except Exception as e:
        return {"success": False, "error": str(e), "charts": [], "summary_items": []}


def generate_inventory_dashboard(df: pd.DataFrame) -> dict:
    """Generate comprehensive Inventory Management dashboard based on store_variance DataFrame"""
    try:
        semantic_colors = get_semantic_colors()
        charts = []
        summary_items = []

        if df.empty:
            return {
                "success": False,
                "charts": [],
                "summary_items": []
            }

        df_calc = df.copy()

        # Calculate derived metrics
        df_calc['Return To Store In Amount'] = (
            df_calc.get('Return To Store In Qty', 0) * df_calc.get('WAC(incl.tax,etc)', 0)
        )

        df_calc['Period_Net_Inward'] = (
            df_calc.get('Purchase Amount', 0) +
            df_calc.get('Ibt In Amount', 0) +
            df_calc.get('Return To Store In Amount', 0)
        )

        df_calc['Period_Net_Outward'] = (
            df_calc.get('Return-Qty Amount', 0) +
            df_calc.get('Indent Amount', 0) +
            df_calc.get('Ibt Out Amount', 0) +
            df_calc.get('Spoilage Amount', 0)
        )

        df_calc['Period_Net_Movement'] = df_calc['Period_Net_Inward'] - df_calc['Period_Net_Outward']
        df_calc['Avg_Inventory'] = (df_calc.get('Opening Amount', 0) + df_calc.get('Closing Amount', 0)) / 2
        df_calc['Period_Turnover'] = df_calc.apply(
            lambda row: row['Period_Net_Outward'] / row['Avg_Inventory'] if row['Avg_Inventory'] > 0 else 0, axis=1
        )

        # === COMPREHENSIVE INVENTORY MOVEMENT KPIs (15 Cards) ===

        # Calculate all movement types - Convert to native Python types
        total_opening = float(df_calc.get('Opening Amount', pd.Series([0])).sum())
        total_closing = float(df_calc.get('Closing Amount', pd.Series([0])).sum())
        net_growth = float(((total_closing - total_opening) / total_opening) * 100) if total_opening > 0 else 0.0

        total_purchases = float(df_calc.get('Purchase Amount', pd.Series([0])).sum())
        total_indents = float(df_calc.get('Indent Amount', pd.Series([0])).sum())
        total_ibt_in = float(df_calc.get('Ibt In Amount', pd.Series([0])).sum())
        total_ibt_out = float(df_calc.get('Ibt Out Amount', pd.Series([0])).sum())
        total_spoilage = float(df_calc.get('Spoilage Amount', pd.Series([0])).sum())
        total_returns_to_store = float(df_calc.get('Return To Store In Amount', pd.Series([0])).sum())
        total_returns_to_vendor = float(df_calc.get('Return-Qty Amount', pd.Series([0])).sum())

        total_inward = float(df_calc['Period_Net_Inward'].sum())
        total_outward = float(df_calc['Period_Net_Outward'].sum())
        avg_turnover = float(df_calc['Period_Turnover'].mean()) if not df_calc['Period_Turnover'].empty else 0.0

        # Row 1: Stock Position (Consolidated with Growth %)
        summary_items.extend([
            {
                "icon": "trending_up",
                "value": f"{total_opening:,.0f}",
                "label": "Opening Stock Value (₹)",
                "data_type": "currency"
            },
            {
                "icon": "account_balance_wallet" if net_growth >= 0 else "trending_down",
                "value": f"{total_closing:,.0f}",
                "label": f"Closing Stock Value (₹) ({net_growth:+.1f}%)",
                "data_type": "currency",
                "secondary_info": f"Growth: {net_growth:+.1f} from opening",
                "trend": "positive" if net_growth >= 0 else "negative"
            }
        ])

        # Row 2: Inward Movements (Items Coming IN)
        summary_items.extend([
            {
                "icon": "shopping_cart",
                "value": f"{total_purchases:,.0f}",
                "label": "Purchases from Vendor (₹)",
                "data_type": "currency"
            },
            {
                "icon": "input",
                "value": f"{total_ibt_in:,.0f}",
                "label": "IBT In from Locations (₹)",
                "data_type": "currency"
            },
            {
                "icon": "keyboard_return",
                "value": f"{total_returns_to_store:,.0f}",
                "label": "Returns to Store from Work Areas (₹)",
                "data_type": "currency"
            }
        ])

        # Row 3: Outward Movements (Items Going OUT)
        summary_items.extend([
            {
                "icon": "construction",
                "value": f"{total_indents:,.0f}",
                "label": "Indents/Usage Consumed (₹)",
                "data_type": "currency"
            },
            {
                "icon": "output",
                "value": f"{total_ibt_out:,.0f}",
                "label": "IBT Out to Locations (₹)",
                "data_type": "currency"
            },
            {
                "icon": "undo",
                "value": f"{total_returns_to_vendor:,.0f}",
                "label": "Returns to Vendor (₹)",
                "data_type": "currency"
            }
        ])

        # Row 4: Loss & Efficiency Metrics (Consolidated Spoilage)
        active_skus = int(len(df_calc[df_calc['Period_Net_Outward'] > 0]))
        total_skus = int(len(df_calc))
        spoilage_rate = float((total_spoilage / total_outward * 100)) if total_outward > 0 else 0.0

        summary_items.extend([
            {
                "icon": "warning" if spoilage_rate > 5.0 else "info",
                "value": f"{total_spoilage:,.0f}",
                "label": f"Spoilage/Loss (₹) ({spoilage_rate:.1f}%)",
                "data_type": "currency",
                "secondary_info": f"Rate: {spoilage_rate:.1f} of total outward movement",
                "trend": "negative" if spoilage_rate > 3.0 else "neutral"
            },
            {
                "icon": "inventory",
                "value": f"{active_skus}/{total_skus}",
                "label": "Active SKUs",
                "data_type": "text",
                "secondary_info": f"Utilization: {(active_skus/total_skus*100):.1f} of inventory items active"
            }
        ])

        # Row 5: Summary Totals & Performance (Enhanced Turnover Context)
        # Determine turnover performance level and context
        if avg_turnover >= 2.0:
            turnover_status = "Excellent"
            turnover_icon = "trending_up"
            turnover_trend = "positive"
            turnover_context = "Outstanding cash flow efficiency"
        elif avg_turnover >= 1.5:
            turnover_status = "Good"
            turnover_icon = "thumb_up"
            turnover_trend = "positive"
            turnover_context = "Healthy inventory performance"
        elif avg_turnover >= 1.0:
            turnover_status = "Average"
            turnover_icon = "sync"
            turnover_trend = "neutral"
            turnover_context = "Room for improvement"
        else:
            turnover_status = "Poor"
            turnover_icon = "warning"
            turnover_trend = "negative"
            turnover_context = "Needs immediate attention"

        # Calculate net movement for enhanced insights
        net_movement = total_inward - total_outward
        net_movement_status = "positive" if net_movement > 0 else "negative" if net_movement < 0 else "neutral"

        summary_items.extend([
            {
                "icon": "sync_alt" if net_movement_status == "neutral" else "trending_up" if net_movement_status == "positive" else "trending_down",
                "value": f"₹{abs(net_movement):,.0f}",
                "label": f"Net Movement ({'Build-up' if net_movement > 0 else 'Consumption' if net_movement < 0 else 'Balanced'})",
                "data_type": "currency",
                "secondary_info": f"In: ₹{total_inward:,.0f} | Out: ₹{total_outward:,.0f} | {'Inventory increasing' if net_movement > 0 else 'Inventory decreasing' if net_movement < 0 else 'Perfectly balanced'}",
                "trend": net_movement_status
            },
            {
                "icon": turnover_icon,
                "value": f"{avg_turnover:.2f}x",
                "label": f"Turnover Efficiency ({turnover_status})",
                "data_type": "number",
                "secondary_info": f"{turnover_context} | Industry benchmark: 1.5-2.0x | {'🚀 Excellent cash flow!' if avg_turnover >= 2.0 else '👍 Good performance' if avg_turnover >= 1.5 else '⚠️ Needs improvement' if avg_turnover >= 1.0 else '🚨 Urgent attention needed'}",
                "trend": turnover_trend
            }
        ])

        # === CHARTS SECTION ===

        # 1. STRATEGIC: Complete Inventory Movement Tracking - FINANCIAL OVERVIEW (FIRST CHART - FULL WIDTH)
        if all(col in df.columns for col in ['Opening Amount', 'Purchase Amount', 'Indent Amount', 'Spoilage Amount', 'Closing Amount']):
            # Calculate all movements with proper visibility for small amounts
            opening_val = float(df_calc.get('Opening Amount', pd.Series([0])).sum())
            purchases_val = float(df_calc.get('Purchase Amount', pd.Series([0])).sum())
            ibt_in_val = float(df_calc.get('Ibt In Amount', pd.Series([0])).sum())
            returns_in_val = float(df_calc.get('Return To Store In Amount', pd.Series([0])).sum())  # Items returned TO store
            returns_out_val = float(df_calc.get('Return-Qty Amount', pd.Series([0])).sum())  # Items returned to vendor
            consumption_val = float(df_calc.get('Indent Amount', pd.Series([0])).sum())
            ibt_out_val = float(df_calc.get('Ibt Out Amount', pd.Series([0])).sum())
            spoilage_val = float(df_calc.get('Spoilage Amount', pd.Series([0])).sum())
            closing_val = float(df_calc.get('Closing Amount', pd.Series([0])).sum())

            # Ensure spoilage is visible even if small - minimum 1% of total flow
            total_flow = opening_val + purchases_val + ibt_in_val + returns_in_val
            min_visible = total_flow * 0.01 if total_flow > 0 else 100
            spoilage_display = max(spoilage_val, min_visible) if spoilage_val > 0 else spoilage_val

            flow_data = {
                'Opening Stock': opening_val,
                '+ Purchases': purchases_val,
                '+ IBT Received': ibt_in_val,
                '+ Returns to Store': returns_in_val,  # Items returned TO store from work areas
                '- Returns to Vendor': -returns_out_val,  # Items returned to vendor
                '- Consumption/Usage': -consumption_val,
                '- IBT Sent Out': -ibt_out_val,
                '- Spoilage/Loss': -spoilage_display,
                'Closing Stock': closing_val
            }

            waterfall_labels = list(flow_data.keys())
            waterfall_values = [float(val) for val in flow_data.values()]

            charts.append({
                "id": "complete_inventory_flow",
                "title": "💰 Complete Inventory Movement Tracking - FINANCIAL OVERVIEW",
                "subtitle": "Strategic: Track every rupee from opening to closing stock",
                "type": "bar",
                "size": "full",
                "data": {
                    "labels": waterfall_labels,
                    "datasets": [{
                        "label": "Amount (₹)",
                        "data": waterfall_values,
                        "backgroundColor": [
                            semantic_colors['info'],      # Opening - Soft blue
                            semantic_colors['positive'],  # Purchases - Sage green
                            semantic_colors['inward'],    # IBT In - Soft blue
                            semantic_colors['positive'],  # Returns to Store - Sage green (inward)
                            semantic_colors['outward'],   # Returns to Vendor - Dusty rose (outward)
                            semantic_colors['primary'],   # Consumption - Orange primary
                            semantic_colors['outward'],   # IBT Out - Dusty rose
                            semantic_colors['spoilage'],  # Spoilage - Dusty rose
                            semantic_colors['info']       # Closing - Soft blue
                        ],
                        "borderColor": '#FFFFFF',
                        "borderWidth": 2,
                        "borderRadius": 6
                    }]
                },
                "options": {
                    "responsive": True,
                    "maintainAspectRatio": False,
                    "plugins": {
                        "legend": {"display": False},
                        "title": {
                            "display": True,
                            "text": "Every movement tracked - No inventory unaccounted",
                            "font": {"size": 12, "style": "italic"},
                            "color": "#666"
                        },
                        "tooltip": {
                            "callbacks": {
                                "title": "function(context) { return context[0].label; }",
                                "label": "function(context) { var val = Math.abs(context.parsed.y); var actual = " + str(spoilage_val) + "; if(context.label.includes('Spoilage') && actual !== val) { return 'Actual Spoilage: ₹' + actual.toLocaleString() + ' (Enhanced for visibility)'; } return 'Amount: ₹' + val.toLocaleString(); }",
                                "afterLabel": "function(context) { if(context.parsed.y > 0) return '↗️ Inflow'; else if(context.parsed.y < 0) return '↘️ Outflow'; else return '📊 Stock Level'; }"
                            }
                        }
                    },
                    "scales": {
                        "x": {
                            "grid": {"display": False},
                            "ticks": {
                                "font": {"size": 10},
                                "maxRotation": 45
                            }
                        },
                        "y": {
                            "grid": {"color": "#E5E5E5"},
                            "ticks": {
                                "font": {"size": 10},
                                "callback": "function(value) { return '₹' + (Math.abs(value)/1000).toFixed(0) + 'K'; }"
                            }
                        }
                    }
                }
            })

        # 2. STRATEGIC: Smart Movement Analysis - OPERATIONAL EFFICIENCY INSIGHTS (MOVED TO SECOND POSITION)
        if 'Category' in df.columns:
            movement_analysis = df_calc.groupby('Category').agg({
                'Purchase Amount': 'sum',
                'Ibt In Amount': 'sum',
                'Return To Store In Amount': 'sum',  # Returns TO store (inward)
                'Return-Qty Amount': 'sum',  # Returns to vendor (outward)
                'Indent Amount': 'sum',
                'Ibt Out Amount': 'sum',
                'Spoilage Amount': 'sum'
            }).fillna(0)

            # Calculate smart movement metrics
            movement_analysis['Total_Inflow'] = (
                movement_analysis['Purchase Amount'] +
                movement_analysis['Ibt In Amount'] +
                movement_analysis['Return To Store In Amount']
            )

            movement_analysis['Total_Outflow'] = (
                movement_analysis['Indent Amount'] +
                movement_analysis['Ibt Out Amount'] +
                movement_analysis['Return-Qty Amount'] +
                movement_analysis['Spoilage Amount']
            )

            movement_analysis['Activity_Level'] = movement_analysis['Total_Inflow'] + movement_analysis['Total_Outflow']

            if not movement_analysis.empty:
                # Take top 6 most active categories
                top_active_categories = movement_analysis.nlargest(6, 'Activity_Level')

                # Create movement insights info
                movement_info_text = """
                🔄 Smart Movement Analysis - Understand Your Inventory Flow Patterns:

                📊 What This Shows:
                • Green Bars: INFLOW - Money flowing INTO inventory (Purchases + IBT Received + Returns to Store)
                • Red Bars: OUTFLOW - Money flowing OUT of inventory (Consumption/Usage + IBT Sent Out + Returns to Vendor + Spoilage/Loss)
                • Net Balance: Whether category is building up stock (positive) or consuming (negative)

                💡 Business Intelligence:
                • High Inflow + High Outflow = Active, fast-moving categories (good cash flow)
                • High Inflow + Low Outflow = Stock building up (check if intentional)
                • Low Inflow + High Outflow = Stock depleting (may need replenishment)
                • Balanced Flow = Optimal inventory management

                🎯 Management Actions:
                • Focus on categories with highest activity levels
                • Investigate imbalanced flows for optimization opportunities
                • Use this data to adjust purchasing and inventory policies
                """

                categories = top_active_categories.index.tolist()
                # Ensure data is in proper rupee format (not percentages)
                inflow_data = [max(0, float(val)) for val in top_active_categories['Total_Inflow'].round(2).tolist()]
                outflow_data = [max(0, float(val)) for val in top_active_categories['Total_Outflow'].round(2).tolist()]

                charts.append({
                    "id": "smart_movement_analysis",
                    "title": "💰 Smart Movement Analysis - RUPEE VALUE TRACKING",
                    "subtitle": "Strategic: Category activity and flow balance for operational optimization",
                    "info": movement_info_text,
                    "type": "bar",
                    "size": "full",  # MADE FULL WIDTH
                    "dataType": "currency",  # EXPLICIT CURRENCY FORMATTING
                    "data": {
                        "labels": categories,
                        "datasets": [
                            {
                                "label": "💰 Inflow (Purchases + IBT Received + Returns to Store)",
                                "data": inflow_data,
                                "backgroundColor": semantic_colors['inward'],
                                "borderColor": semantic_colors['inward'],
                                "borderWidth": 2,
                                "borderRadius": 6
                            },
                            {
                                "label": "💸 Outflow (Consumption/Usage + IBT Sent Out + Returns to Vendor + Spoilage/Loss)",
                                "data": outflow_data,
                                "backgroundColor": semantic_colors['outward'],
                                "borderColor": semantic_colors['outward'],
                                "borderWidth": 2,
                                "borderRadius": 6
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 12, "weight": "bold"}
                                }
                            },
                            "tooltip": {
                                "mode": "index",
                                "intersect": False,
                                "enabled": True,
                                "external": None,
                                "callbacks": {
                                    "title": "function(context) { return '📊 Category: ' + context[0].label; }",
                                    "label": "function(context) { var value = Math.abs(context.parsed.y); var formattedValue = value >= 100000 ? '₹' + (value/100000).toFixed(1) + 'L' : value >= 1000 ? '₹' + (value/1000).toFixed(0) + 'K' : '₹' + value.toLocaleString(); return context.dataset.label + ': ' + formattedValue; }",
                                    "afterBody": "function(context) { var inflow = context[0] ? Math.abs(context[0].parsed.y) : 0; var outflow = context[1] ? Math.abs(context[1].parsed.y) : 0; var netFlow = inflow - outflow; var status = netFlow > 0 ? '📈 Building Stock' : netFlow < 0 ? '📉 Consuming Stock' : '⚖️ Balanced'; var totalActivity = inflow + outflow; var formattedNet = Math.abs(netFlow) >= 100000 ? '₹' + (Math.abs(netFlow)/100000).toFixed(1) + 'L' : Math.abs(netFlow) >= 1000 ? '₹' + (Math.abs(netFlow)/1000).toFixed(0) + 'K' : '₹' + Math.abs(netFlow).toLocaleString(); var formattedActivity = totalActivity >= 100000 ? '₹' + (totalActivity/100000).toFixed(1) + 'L' : totalActivity >= 1000 ? '₹' + (totalActivity/1000).toFixed(0) + 'K' : '₹' + totalActivity.toLocaleString(); return ['Net Flow: ' + (netFlow >= 0 ? '+' : '-') + formattedNet, 'Status: ' + status, 'Activity Level: ' + formattedActivity]; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "type": "linear",
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "💰 Movement Value (₹)",
                                    "font": {"size": 12, "weight": "bold"},
                                    "color": "#2C3E50"
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "maxTicksLimit": 8,
                                    "stepSize": None,
                                    "callback": "function(value) { if (value >= 100000) { return '₹' + (value/100000).toFixed(1) + 'L'; } else if (value >= 1000) { return '₹' + (value/1000).toFixed(0) + 'K'; } else if (value >= 1) { return '₹' + value.toFixed(0); } else { return '₹' + value.toFixed(2); } }"
                                }
                            }
                        }
                    }
                })

        # 3. CRITICAL: Physical vs System Stock Variance - IMMEDIATE ACTION REQUIRED
        if all(col in df.columns for col in ['Physical Closing Qty', 'Closing Qty', 'Variance Amount', 'Category']):
            # Calculate variance analysis by category
            variance_analysis = df_calc.groupby('Category').agg({
                'Variance Amount': 'sum',
                'Physical Closing Amount': 'sum',
                'Closing Amount': 'sum',
                'Item Code': 'count'
            }).round(2)

            # Calculate variance percentage
            variance_analysis['Variance_Percentage'] = (
                variance_analysis['Variance Amount'] / variance_analysis['Closing Amount'] * 100
            ).fillna(0)

            # Filter categories with significant variances
            significant_variances = variance_analysis[
                (abs(variance_analysis['Variance Amount']) > 100) |
                (abs(variance_analysis['Variance_Percentage']) > 1.0)
            ].nlargest(8, 'Variance Amount', keep='all')

            if not significant_variances.empty:
                categories = significant_variances.index.tolist()
                variance_amounts = [float(val) for val in significant_variances['Variance Amount'].tolist()]
                variance_percentages = [float(val) for val in significant_variances['Variance_Percentage'].tolist()]

                # Color code based on variance severity using semantic colors
                variance_colors = []
                for amount, percentage in zip(variance_amounts, variance_percentages):
                    if abs(amount) > 5000 or abs(percentage) > 10:
                        variance_colors.append(semantic_colors['negative'])  # Critical - Dusty rose
                    elif abs(amount) > 2000 or abs(percentage) > 5:
                        variance_colors.append(semantic_colors['warning'])  # High - Warning orange
                    elif abs(amount) > 500 or abs(percentage) > 2:
                        variance_colors.append(semantic_colors['primary'])  # Medium - Orange primary
                    else:
                        variance_colors.append(semantic_colors['positive'])  # Low - Sage green

                # Create info text for variance analysis
                variance_info_text = """
                🚨 Physical vs System Stock Variance - IMMEDIATE ACTION REQUIRED

                📊 What This Shows:
                • Variance Amount: Difference between physical count and system records (₹)
                • Positive variance: Physical stock is higher than system records
                • Negative variance: Physical stock is lower than system records (potential shrinkage)

                ⚠️ Why This Is CRITICAL:
                • Large variances indicate inventory control issues
                • Consistent negative variances suggest theft, spoilage, or recording errors
                • Positive variances may indicate missed receipts or recording delays
                • Accurate inventory is crucial for cost control and ordering decisions

                🎯 URGENT Action Required:
                • 🔴 Critical (>₹5K or >10%): Immediate investigation needed
                • 🟡 High (>₹2K or >5%): Review processes and controls
                • 🟡 Medium (>₹500 or >2%): Monitor closely, improve procedures
                • 🟢 Low (<₹500 and <2%): Acceptable variance range
                """

                charts.append({
                    "id": "physical_vs_system_variance",
                    "title": "🚨 Physical vs System Stock Variance - IMMEDIATE ACTION",
                    "subtitle": "Critical: Identify discrepancies between physical counts and system records",
                    "info": variance_info_text,
                    "type": "bar",
                    "data": {
                        "labels": categories,
                        "datasets": [{
                            "label": "Variance Amount (₹)",
                            "data": variance_amounts,
                            "backgroundColor": variance_colors,
                            "borderColor": '#FFFFFF',
                            "borderWidth": 2,
                            "borderRadius": 6
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {"display": False},
                            "title": {
                                "display": True,
                                "text": "🔴 Critical | 🟠 High | 🟡 Medium | 🟢 Low Variance - Address Critical Issues First",
                                "font": {"size": 11, "style": "italic"},
                                "color": "#666"
                            },
                            "tooltip": {
                                "callbacks": {
                                    "title": "function(context) { return '⚠️ URGENT: ' + context[0].label; }",
                                    "label": "function(context) { var amount = context.parsed.y; var percentages = " + str(variance_percentages) + "; var percentage = percentages[context.dataIndex]; return 'Variance: ₹' + amount.toLocaleString() + ' (' + percentage.toFixed(2) + '%)'; }",
                                    "afterLabel": "function(context) { var amount = context.parsed.y; if(amount > 0) return '📈 Physical > System (Excess found)'; else if(amount < 0) return '📉 Physical < System (Shortage detected)'; else return '✅ Perfect match'; }",
                                    "footer": "function(context) { var amount = Math.abs(context.parsed.y); var percentage = Math.abs(" + str(variance_percentages) + "[context.dataIndex]); if(amount > 5000 || percentage > 10) return '🚨 CRITICAL: Immediate investigation required'; else if(amount > 2000 || percentage > 5) return '⚠️ HIGH: Review processes'; else if(amount > 500 || percentage > 2) return '📋 MEDIUM: Monitor closely'; else return '✅ LOW: Acceptable variance'; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Variance Amount (₹) - Positive = Excess, Negative = Shortage",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(1) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 4. CRITICAL: Smart Risk Intelligence - URGENT ATTENTION NEEDED
        if 'Spoilage Amount' in df.columns and 'Category' in df.columns:
            # Comprehensive risk analysis combining multiple factors
            risk_analysis = df_calc.groupby('Category').agg({
                'Spoilage Amount': 'sum',
                'Period_Turnover': 'mean',
                'Closing Amount': 'sum',
                'Opening Amount': 'sum',
                'Item Code': 'count'
            }).fillna(0)

            # Calculate comprehensive risk metrics
            risk_analysis['Spoilage_Rate'] = (risk_analysis['Spoilage Amount'] / risk_analysis['Closing Amount'] * 100).fillna(0)
            risk_analysis['Turnover_Risk'] = risk_analysis['Period_Turnover'].apply(lambda x: 10 if x < 0.5 else (5 if x < 1.0 else (2 if x < 2.0 else 0)))
            risk_analysis['Value_Risk'] = (risk_analysis['Closing Amount'] / risk_analysis['Closing Amount'].sum() * 100).apply(lambda x: 10 if x > 20 else (5 if x > 10 else 0))
            risk_analysis['Overall_Risk_Score'] = risk_analysis['Spoilage_Rate'] + risk_analysis['Turnover_Risk'] + risk_analysis['Value_Risk']

            # Get categories with significant risk (top 6)
            high_risk_categories = risk_analysis[risk_analysis['Overall_Risk_Score'] > 0].nlargest(6, 'Overall_Risk_Score')

            if not high_risk_categories.empty:
                categories = high_risk_categories.index.tolist()
                spoilage_rates = [float(val) for val in high_risk_categories['Spoilage_Rate'].round(2).tolist()]
                risk_scores = [float(val) for val in high_risk_categories['Overall_Risk_Score'].round(1).tolist()]

                # Color code based on comprehensive risk level using semantic colors
                risk_colors = []
                risk_labels = []
                for score in risk_scores:
                    if score > 20:
                        risk_colors.append(semantic_colors['negative'])  # Critical - Dusty rose
                        risk_labels.append('🚨 Critical')
                    elif score > 15:
                        risk_colors.append(semantic_colors['negative'])  # High - Dusty rose
                        risk_labels.append('🔴 High Risk')
                    elif score > 10:
                        risk_colors.append(semantic_colors['warning'])  # Medium - Warning orange
                        risk_labels.append('⚠️ Medium Risk')
                    else:
                        risk_colors.append(semantic_colors['primary'])  # Low - Orange primary
                        risk_labels.append('⚡ Watch')

                # Create comprehensive risk info
                risk_info_text = """
                🚨 Smart Risk Intelligence - URGENT ATTENTION NEEDED

                🎯 What This Analysis Does:
                • Combines 3 critical risk factors: Spoilage Rate + Turnover Speed + Value Impact
                • Identifies categories that could hurt your bottom line the most
                • Prioritizes where to focus your management attention first

                📊 Risk Score Components:
                • Spoilage Risk: High waste rates = direct profit loss
                • Turnover Risk: Slow-moving inventory = cash tied up
                • Value Risk: High-value categories = bigger financial impact

                💰 Why This Matters:
                • High-risk categories can drain profits quickly
                • Early identification prevents bigger losses
                • Focused action on top risks gives maximum ROI

                🎯 URGENT Action Guide by Risk Level:
                • 🚨 Critical (20+): URGENT - Daily monitoring, immediate process changes
                • 🔴 High (15+): Weekly reviews, implement controls, staff training
                • ⚠️ Medium (10+): Monthly checks, process improvements
                • ⚡ Watch (<10): Quarterly review, maintain current practices
                """

                charts.append({
                    "id": "smart_risk_intelligence",
                    "title": "🚨 Smart Risk Intelligence - URGENT ATTENTION NEEDED",
                    "subtitle": "Critical: Multi-factor risk assessment to prioritize management attention",
                    "info": risk_info_text,
                    "type": "bar",
                    "data": {
                        "labels": categories,
                        "datasets": [
                            {
                                "label": "🔍 Spoilage Rate (%)",
                                "data": spoilage_rates,
                                "backgroundColor": risk_colors,
                                "borderColor": '#FFFFFF',
                                "borderWidth": 2,
                                "borderRadius": 6,
                                "yAxisID": 'y'
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {"display": False},
                            "title": {
                                "display": True,
                                "text": "🚨 Critical | 🔴 High Risk | ⚠️ Medium Risk | ⚡ Watch - Focus on highest risk first",
                                "font": {"size": 11, "style": "italic"},
                                "color": "#666"
                            },
                            "tooltip": {
                                "callbacks": {
                                    "title": "function(context) { return '⚠️ Risk Category: ' + context[0].label; }",
                                    "label": "function(context) { var spoilageRate = context.parsed.y; var riskLabels = " + str(risk_labels) + "; var riskScores = " + str(risk_scores) + "; return ['Spoilage Rate: ' + spoilageRate.toFixed(2) + '%', 'Overall Risk Score: ' + riskScores[context.dataIndex]]; }",
                                    "afterLabel": "function(context) { var riskLabels = " + str(risk_labels) + "; return 'Risk Level: ' + riskLabels[context.dataIndex]; }",
                                    "footer": "function(context) { var riskLabels = " + str(risk_labels) + "; var label = riskLabels[context.dataIndex]; if(label.includes('Critical')) return '🚨 URGENT: Daily monitoring, immediate action required'; else if(label.includes('High')) return '🔴 HIGH: Weekly reviews, implement controls'; else if(label.includes('Medium')) return '⚠️ MEDIUM: Monthly checks, process improvements'; else return '⚡ WATCH: Quarterly review, maintain practices'; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Spoilage Rate (%) - Visual indicator of risk level",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return value.toFixed(1) + '%'; }"
                                }
                            }
                        }
                    }
                })

        # 💰 TIER 2: STRATEGIC MANAGEMENT (High Value)



        # 4. STRATEGIC: Top 10 Investment Items - FOCUS ON HIGH-VALUE ASSETS
        if 'Item Name' in df.columns and 'Closing Amount' in df.columns and 'Opening Amount' in df.columns:
            # Get top 10 items by current value for focused analysis
            top_items_analysis = df_calc.nlargest(10, 'Closing Amount')[
                ['Item Name', 'Item Code', 'Category', 'Closing Amount', 'Opening Amount', 'Period_Turnover']
            ].copy()

            if not top_items_analysis.empty:
                # Create simple info text for finance team
                high_value_info_text = """
                📊 Your Top 10 Most Expensive Inventory Items:

                • These items represent the biggest chunk of your inventory investment
                • Compare current vs opening stock to see if money is tied up or freed up
                • Focus on these items to control inventory costs and cash flow
                • Green bars show opening values, Blue bars show current values
                """

                item_names = [name[:20] + '...' if len(name) > 20 else name for name in top_items_analysis['Item Name'].tolist()]

                charts.append({
                    "id": "top_investment_items",
                    "title": "💰 Top 10 Investment Items - FOCUS ON HIGH-VALUE ASSETS",
                    "subtitle": "Strategic: Your highest-value inventory items requiring focused management",
                    "info": high_value_info_text,
                    "type": "bar",
                    "data": {
                        "labels": item_names,
                        "datasets": [
                            {
                                "label": "Opening Stock Value (₹)",
                                "data": [float(val) for val in top_items_analysis['Opening Amount'].round(2).tolist()],
                                "backgroundColor": semantic_colors['positive'],
                                "borderColor": semantic_colors['positive'],
                                "borderWidth": 1,
                                "borderRadius": 4
                            },
                            {
                                "label": "Current Stock Value (₹)",
                                "data": [float(val) for val in top_items_analysis['Closing Amount'].round(2).tolist()],
                                "backgroundColor": semantic_colors['info'],
                                "borderColor": semantic_colors['info'],
                                "borderWidth": 1,
                                "borderRadius": 4
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 12, "weight": "bold"},
                                    "color": "#2C3E50",
                                    "generateLabels": "function(chart) { var data = chart.data; return data.datasets.map(function(dataset, i) { return { text: dataset.label, fillStyle: dataset.backgroundColor, strokeStyle: dataset.borderColor, lineWidth: 2, pointStyle: 'circle' }; }); }"
                                }
                            },
                            "tooltip": {
                                "mode": "index",
                                "intersect": False,
                                "callbacks": {
                                    "title": "function(context) { return '" + str(top_items_analysis['Item Name'].tolist()) + "'[context[0].dataIndex]; }",
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString(); }",
                                    "afterBody": "function(context) { var opening = context.length > 0 ? context[0].parsed.y : 0; var current = context.length > 1 ? context[1].parsed.y : 0; var change = opening > 0 ? ((current - opening) / opening * 100).toFixed(1) : 'N/A'; var turnover = [" + str([float(t) for t in top_items_analysis['Period_Turnover'].tolist()]) + "][context[0].dataIndex]; var category = [" + str(top_items_analysis['Category'].tolist()) + "][context[0].dataIndex]; return 'Change: ' + (change !== 'N/A' ? (change > 0 ? '+' : '') + change + '%' : 'N/A') + ' | Turnover: ' + turnover.toFixed(2) + 'x | Category: ' + category; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {
                                    "font": {"size": 9},
                                    "maxRotation": 45
                                }
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Value (₹)",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 3. Inventory Value Distribution by Category (Clear business breakdown)
        if 'Category' in df.columns and 'Closing Amount' in df.columns:
            category_analysis = df_calc.groupby('Category').agg({
                'Closing Amount': 'sum',
                'Opening Amount': 'sum',
                'Item Code': 'count'
            }).sort_values('Closing Amount', ascending=False)

            if not category_analysis.empty:
                # Take top 8 categories for clarity
                top_categories = category_analysis.head(8)

                charts.append({
                    "id": "inventory_by_category",
                    "title": "Inventory Value by Category: Business Portfolio Overview",
                    "subtitle": "Understand which product categories drive your inventory investment",
                    "type": "bar",
                    "data": {
                        "labels": top_categories.index.tolist(),
                        "datasets": [
                            {
                                "label": "Current Stock Value (₹)",
                                "data": [float(val) for val in top_categories['Closing Amount'].round(2).tolist()],
                                "backgroundColor": semantic_colors['info'],
                                "borderColor": semantic_colors['info'],
                                "borderWidth": 1,
                                "borderRadius": 4
                            },
                            {
                                "label": "Opening Stock Value (₹)",
                                "data": [float(val) for val in top_categories['Opening Amount'].round(2).tolist()],
                                "backgroundColor": semantic_colors['positive'],
                                "borderColor": semantic_colors['positive'],
                                "borderWidth": 1,
                                "borderRadius": 4
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 12, "weight": "bold"},
                                    "color": "#2C3E50",
                                    "generateLabels": "function(chart) { var data = chart.data; return data.datasets.map(function(dataset, i) { return { text: dataset.label, fillStyle: dataset.backgroundColor, strokeStyle: dataset.borderColor, lineWidth: 2, pointStyle: 'circle' }; }); }"
                                }
                            },
                            "tooltip": {
                                "mode": "index",
                                "intersect": False,
                                "callbacks": {
                                    "title": "function(context) { return 'Category: ' + context[0].label; }",
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString(); }",
                                    "afterBody": "function(context) { var current = context[0].parsed.y; var opening = context[1] ? context[1].parsed.y : 0; var change = opening > 0 ? ((current - opening) / opening * 100).toFixed(1) : 'N/A'; return 'Change: ' + (change !== 'N/A' ? (change > 0 ? '+' : '') + change + '%' : 'N/A'); }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Value (₹)",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 4. IBT (Inter-Branch Transfer) Analysis - Track inventory movements between locations
        if all(col in df.columns for col in ['Ibt In Amount', 'Ibt Out Amount', 'Location']):
            # Calculate IBT activity by location
            ibt_analysis = df_calc.groupby('Location').agg({
                'Ibt In Amount': 'sum',
                'Ibt Out Amount': 'sum',
                'Item Code': 'count'
            }).round(2)

            # Filter locations with IBT activity
            active_ibt_locations = ibt_analysis[(ibt_analysis['Ibt In Amount'] > 0) | (ibt_analysis['Ibt Out Amount'] > 0)]

            if not active_ibt_locations.empty:
                locations = active_ibt_locations.index.tolist()
                ibt_in_values = [float(val) for val in active_ibt_locations['Ibt In Amount'].tolist()]
                ibt_out_values = [float(val) for val in active_ibt_locations['Ibt Out Amount'].tolist()]

                # Create info text for IBT analysis
                ibt_info_text = """
                🔄 Inter-Branch Transfer (IBT) Analysis shows inventory movement between your locations:

                📊 What This Shows:
                • IBT In: Inventory received from other branches (inward movement)
                • IBT Out: Inventory sent to other branches (outward movement)
                • Net Transfer: Overall transfer balance per location

                💡 Business Insights:
                • High IBT In: Location receives significant inventory from other branches
                • High IBT Out: Location supplies inventory to other branches
                • Balanced transfers indicate good inter-branch coordination
                • Imbalanced transfers may indicate supply chain optimization opportunities

                🎯 Management Actions:
                • Monitor transfer patterns to optimize inventory distribution
                • Identify hub locations vs. receiving locations
                • Ensure transfer costs are justified by operational benefits
                • Review transfer frequency and batch sizes for efficiency
                """

                charts.append({
                    "id": "ibt_analysis",
                    "title": "Inter-Branch Transfer Analysis: Inventory Movement Between Locations",
                    "subtitle": "Track how inventory flows between your restaurant locations",
                    "info": ibt_info_text,
                    "type": "bar",
                    "data": {
                        "labels": locations,
                        "datasets": [
                            {
                                "label": "IBT In (Received) ₹",
                                "data": ibt_in_values,
                                "backgroundColor": semantic_colors['inward'],
                                "borderColor": semantic_colors['inward'],
                                "borderWidth": 1,
                                "borderRadius": 4
                            },
                            {
                                "label": "IBT Out (Sent) ₹",
                                "data": ibt_out_values,
                                "backgroundColor": semantic_colors['outward'],
                                "borderColor": semantic_colors['outward'],
                                "borderWidth": 1,
                                "borderRadius": 4
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 12, "weight": "bold"}
                                }
                            },
                            "title": {
                                "display": True,
                                "text": "🟢 Green = Inventory Received | 🔴 Red = Inventory Sent",
                                "font": {"size": 11, "style": "italic"},
                                "color": "#666"
                            },
                            "tooltip": {
                                "mode": "index",
                                "intersect": False,
                                "callbacks": {
                                    "title": "function(context) { return 'Location: ' + context[0].label; }",
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString(); }",
                                    "afterBody": "function(context) { var received = context[0] ? context[0].parsed.y : 0; var sent = context[1] ? context[1].parsed.y : 0; var net = received - sent; return 'Net Transfer: ₹' + net.toLocaleString() + (net > 0 ? ' (Net Receiver)' : net < 0 ? ' (Net Sender)' : ' (Balanced)'); }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Transfer Amount (₹)",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 5. PERFORMANCE: Location Performance Dashboard - MULTI-LOCATION COMPARISON
        if 'Location' in df.columns:
            location_performance = df_calc.groupby('Location').agg({
                'Closing Amount': 'sum',
                'Opening Amount': 'sum',
                'Period_Turnover': 'mean',
                'Spoilage Amount': 'sum',
                'Item Code': 'count'
            }).round(2)

            if not location_performance.empty and len(location_performance) > 1:
                # Calculate meaningful metrics for each location
                location_metrics = []
                for location, row in location_performance.iterrows():
                    growth_rate = ((row['Closing Amount'] - row['Opening Amount']) / row['Opening Amount'] * 100) if row['Opening Amount'] > 0 else 0
                    spoilage_rate = (row['Spoilage Amount'] / row['Closing Amount'] * 100) if row['Closing Amount'] > 0 else 0

                    location_metrics.append({
                        'location': location,
                        'stock_value': float(row['Closing Amount']),
                        'growth_rate': float(growth_rate),
                        'turnover_rate': float(row['Period_Turnover']),
                        'spoilage_rate': float(spoilage_rate),
                        'item_count': int(row['Item Code'])
                    })

                # Sort by stock value for better presentation
                location_metrics.sort(key=lambda x: x['stock_value'], reverse=True)

                locations = [loc['location'] for loc in location_metrics]
                stock_values = [loc['stock_value'] for loc in location_metrics]
                turnover_rates = [loc['turnover_rate'] for loc in location_metrics]

                # Create location performance info
                location_info_text = """
                🏢 Location Performance Dashboard - Compare Your Restaurant Locations:

                📊 Key Metrics Explained:
                • Stock Value: Total inventory investment per location
                • Turnover Rate: How efficiently each location converts inventory to sales
                • Growth Rate: Stock value change from opening to closing
                • Spoilage Rate: Percentage of inventory lost to waste

                🎯 What to Look For:
                • High stock value + High turnover = Excellent performance
                • High stock value + Low turnover = Money tied up, needs attention
                • High spoilage rate = Process improvement needed
                • Consistent performance across locations = Good standardization

                💡 Management Actions:
                • Best performers: Study and replicate their practices
                • Poor performers: Investigate causes and implement improvements
                • Optimize inventory allocation based on location performance
                """

                charts.append({
                    "id": "location_performance_dashboard",
                    "title": "📈 Location Performance - MULTI-LOCATION COMPARISON",
                    "subtitle": "Performance: Compare inventory investment and turnover efficiency across locations",
                    "info": location_info_text,
                    "type": "bar",
                    "data": {
                        "labels": locations,
                        "datasets": [
                            {
                                "label": "📊 Stock Value (₹)",
                                "data": stock_values,
                                "backgroundColor": semantic_colors['info'],
                                "borderColor": semantic_colors['info'],
                                "borderWidth": 2,
                                "borderRadius": 6,
                                "yAxisID": 'y'
                            },
                            {
                                "label": "🔄 Turnover Rate (x)",
                                "data": turnover_rates,
                                "backgroundColor": semantic_colors['warning'],
                                "borderColor": semantic_colors['warning'],
                                "borderWidth": 2,
                                "borderRadius": 6,
                                "yAxisID": 'y1',
                                "type": 'line',
                                "fill": False,
                                "tension": 0.4,
                                "pointRadius": 6,
                                "pointHoverRadius": 8
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "interaction": {
                            "mode": "index",
                            "intersect": False
                        },
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 12, "weight": "bold"}
                                }
                            },
                            "title": {
                                "display": True,
                                "text": "💡 Best locations: High stock value + High turnover rate",
                                "font": {"size": 11, "style": "italic"},
                                "color": "#666"
                            },
                            "tooltip": {
                                "callbacks": {
                                    "title": "function(context) { return '🏢 Location: ' + context[0].label; }",
                                    "label": "function(context) { var metrics = " + str(location_metrics) + "; var metric = metrics[context.dataIndex]; if(context.datasetIndex === 0) { return '📊 Stock Value: ₹' + context.parsed.y.toLocaleString(); } else { return '🔄 Turnover Rate: ' + context.parsed.y.toFixed(2) + 'x'; } }",
                                    "afterBody": "function(context) { var metrics = " + str(location_metrics) + "; var metric = metrics[context[0].dataIndex]; var performance = metric.turnover_rate >= 1.5 ? '🟢 Excellent' : metric.turnover_rate >= 1.0 ? '🟡 Good' : '🔴 Needs Improvement'; return ['Growth Rate: ' + metric.growth_rate.toFixed(1) + '%', 'Spoilage Rate: ' + metric.spoilage_rate.toFixed(1) + '%', 'Items: ' + metric.item_count, 'Performance: ' + performance]; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "type": "linear",
                                "display": True,
                                "position": "left",
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Stock Value (₹)",
                                    "font": {"size": 12},
                                    "color": semantic_colors['info']
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            },
                            "y1": {
                                "type": "linear",
                                "display": True,
                                "position": "right",
                                "beginAtZero": True,
                                "grid": {"drawOnChartArea": False},
                                "title": {
                                    "display": True,
                                    "text": "Turnover Rate (x)",
                                    "font": {"size": 12},
                                    "color": semantic_colors['warning']
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return value.toFixed(1) + 'x'; }"
                                }
                            }
                        }
                    }
                })

        # 6. Work Area Consumption Analysis (Critical operational insight)
        if 'Indent Amount' in df.columns and any('work' in col.lower() for col in df.columns):
            # Get work area related columns
            work_area_cols = [col for col in df.columns if 'work' in col.lower() and 'amount' in col.lower()]

            if work_area_cols:
                work_area_consumption_info = """
                🏭 Work Area Consumption Analysis shows how different operational areas use inventory:

                📊 Why This Matters:
                • Identifies which work areas consume the most inventory value
                • Helps optimize inventory allocation across operations
                • Reveals efficiency differences between work areas
                • Critical for cost control and operational planning

                💡 Key Insights:
                • High Consumption Areas: May need dedicated inventory management
                • Low Consumption Areas: Check if underutilized or efficient
                • Consumption Patterns: Help predict future inventory needs
                • Cost Centers: Identify areas driving inventory costs

                🎯 Management Actions:
                • High consumers: Ensure adequate supply, monitor waste
                • Efficient areas: Study best practices for replication
                • Irregular patterns: Investigate operational changes needed
                """

                # Aggregate work area consumption data
                work_area_data = {}
                for col in work_area_cols:
                    area_name = col.replace(' Amount', '').replace('Total(incl.tax,etc)', '').strip()
                    if area_name and area_name != 'All Work Area Stock':
                        work_area_data[area_name] = float(df_calc.get(col, pd.Series([0])).sum())

                if work_area_data:
                    # Sort by consumption value
                    sorted_areas = dict(sorted(work_area_data.items(), key=lambda x: x[1], reverse=True))
                    top_work_areas = dict(list(sorted_areas.items())[:8])  # Top 8 work areas

                    charts.append({
                        "id": "work_area_consumption_analysis",
                        "title": "Work Area Consumption Analysis: Operational Efficiency Insights",
                        "subtitle": "Track inventory consumption across different operational areas",
                        "info": work_area_consumption_info,
                        "type": "bar",
                        "data": {
                            "labels": list(top_work_areas.keys()),
                            "datasets": [{
                                "label": "💰 Total Consumption Value (₹)",
                                "data": list(top_work_areas.values()),
                                "backgroundColor": semantic_colors['primary'],
                                "borderColor": semantic_colors['primary'],
                                "borderWidth": 2,
                                "borderRadius": 6
                            }]
                        },
                        "options": {
                            "responsive": True,
                            "maintainAspectRatio": False,
                            "plugins": {
                                "legend": {
                                    "display": True,
                                    "position": "top",
                                    "labels": {
                                        "usePointStyle": True,
                                        "padding": 20,
                                        "font": {"size": 12, "weight": "bold"},
                                        "color": "#2C3E50"
                                    }
                                },
                                "title": {
                                    "display": True,
                                    "text": "🎯 Focus on high-consumption areas for cost optimization",
                                    "font": {"size": 11, "style": "italic"},
                                    "color": "#7F8C8D"
                                },
                                "tooltip": {
                                    "callbacks": {
                                        "title": "function(context) { return '🏭 Work Area: ' + context[0].label; }",
                                        "label": "function(context) { var total = context.dataset.data.reduce((a, b) => a + b, 0); var percentage = ((context.parsed.y / total) * 100).toFixed(1); return '💰 Consumption: ₹' + context.parsed.y.toLocaleString() + ' (' + percentage + '% of total)'; }",
                                        "afterLabel": "function(context) { var value = context.parsed.y; if(value > 100000) return '🔴 High consumption - Monitor closely'; else if(value > 50000) return '🟡 Moderate consumption - Regular review'; else return '🟢 Low consumption - Efficient operation'; }"
                                    }
                                }
                            },
                            "scales": {
                                "x": {
                                    "grid": {"display": False},
                                    "ticks": {
                                        "font": {"size": 10, "weight": "bold"},
                                        "color": "#2C3E50",
                                        "maxRotation": 45
                                    },
                                    "title": {
                                        "display": True,
                                        "text": "🏭 Work Areas",
                                        "font": {"size": 12, "weight": "bold"},
                                        "color": "#2C3E50"
                                    }
                                },
                                "y": {
                                    "beginAtZero": True,
                                    "grid": {"color": "#ECF0F1"},
                                    "title": {
                                        "display": True,
                                        "text": "💰 Consumption Value (₹)",
                                        "font": {"size": 12, "weight": "bold"},
                                        "color": "#2C3E50"
                                    },
                                    "ticks": {
                                        "font": {"size": 10, "weight": "bold"},
                                        "color": "#2C3E50",
                                        "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                    }
                                }
                            }
                        }
                    })



        return {
            "success": True,
            "charts": charts,
            "summary_items": summary_items
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "charts": [],
            "summary_items": []
        }





def generate_reconciliation_dashboard(store_variance_df: pd.DataFrame, inventory_consumption_df: pd.DataFrame, department_category_mappings: list = None, sales_data: dict = None, category_workarea_mappings: list = None) -> dict:
    """Generate Reconciliation dashboard in exact tabular format matching the PDF report - NO CHARTS OR SUMMARY CARDS"""
    try:
        charts = []

        # Process department-category mappings
        if department_category_mappings:
            # Create a mapping dictionary for easy lookup: category -> department
            category_to_department = {}
            for mapping in department_category_mappings:
                dept_id = mapping.get('departmentId')
                dept_name = mapping.get('departmentName')
                categories = mapping.get('categories', [])

                for category in categories:
                    category_to_department[category] = {
                        'id': dept_id,
                        'name': dept_name
                    }
        else:
            category_to_department = {}

        if store_variance_df.empty and inventory_consumption_df.empty:
            return {
                "success": True,
                "charts": [],
                "summary_items": [{
                    "icon": "warning",
                    "value": "No Data",
                    "label": "Reconciliation Records",
                    "data_type": "text"
                }]
            }

        # Process the data to create reconciliation table exactly like PDF format
        store_df = store_variance_df.copy() if not store_variance_df.empty else pd.DataFrame()
        consumption_df = inventory_consumption_df.copy() if not inventory_consumption_df.empty else pd.DataFrame()

        # Create reconciliation table data structure
        reconciliation_data = create_reconciliation_table_data(store_df, consumption_df, category_workarea_mappings)

        # Process department-wise sales data
        processed_sales = process_department_sales_data(sales_data) if sales_data else {}

        # Calculate grand totals from reconciliation data
        grand_totals = {
            'opening_stock_store': 0,
            'opening_stock_kitchen': 0,
            'opening_stock_total': 0,
            'purchase': 0,
            'transfer_in_out_store': 0,
            'transfer_in_out_kitchen': 0,
            'closing_stock_store': 0,
            'closing_stock_kitchen': 0,
            'closing_stock_total': 0,
            'consumption': 0
        }

        for category_data in reconciliation_data.values():
            for key in grand_totals.keys():
                grand_totals[key] += category_data['totals'][key]

        # ===== FOOD COST ANALYSIS SECTION (TOP PRIORITY) =====
        if processed_sales and reconciliation_data:
            # Calculate key metrics for summary cards
            total_sales = sum(dept_data.get('sales_amount', 0) for dept_data in processed_sales.values())
            total_consumption = grand_totals['consumption']
            food_cost_percentage = (total_consumption / total_sales * 100) if total_sales > 0 else 0

            # Calculate department-wise data for charts
            department_data = {}
            total_sales_all_departments = 0

            for dept_name, dept_sales in processed_sales.items():
                dept_categories = category_to_department.get(dept_name, []) if category_to_department else []
                dept_consumption = 0

                for category in dept_categories:
                    if category in reconciliation_data:
                        dept_consumption += reconciliation_data[category]['totals']['consumption']

                department_data[dept_name] = {
                    'sales': dept_sales.get('sales_amount', 0),
                    'consumption': dept_consumption,
                    'cost_percentage': (dept_consumption / dept_sales.get('sales_amount', 1) * 100) if dept_sales.get('sales_amount', 0) > 0 else 0
                }
                total_sales_all_departments += dept_sales.get('sales_amount', 0)

            # Add Food Cost Analysis Summary Cards
            charts.append({
                "id": "food_cost_summary_cards",
                "title": "📊 Food Cost Analysis - Key Metrics",
                "type": "summary_cards",
                "size": "full",
                "data": {
                    "cards": [
                        {
                            "title": "Total Sales",
                            "value": format_indian_currency(total_sales),
                            "subtitle": "Revenue across all departments",
                            "color": "#2E7D32",
                            "icon": "💰"
                        },
                        {
                            "title": "Total Food Cost",
                            "value": format_indian_currency(total_consumption),
                            "subtitle": "Total consumption cost",
                            "color": "#D84315",
                            "icon": "🍽️"
                        },
                        {
                            "title": "Food Cost %",
                            "value": f"{food_cost_percentage:.1f}%",
                            "subtitle": "Cost as % of sales",
                            "color": "#F57C00" if food_cost_percentage > 30 else "#388E3C",
                            "icon": "📈"
                        },
                        {
                            "title": "Departments",
                            "value": str(len(department_data)),
                            "subtitle": "Active departments",
                            "color": "#1976D2",
                            "icon": "🏢"
                        }
                    ]
                }
            })

            # Add Department-wise Food Cost Chart
            if len(department_data) > 1:
                dept_names = list(department_data.keys())
                dept_sales = [department_data[dept]['sales'] for dept in dept_names]
                dept_consumption = [department_data[dept]['consumption'] for dept in dept_names]
                dept_cost_pct = [department_data[dept]['cost_percentage'] for dept in dept_names]

                charts.append({
                    "id": "department_food_cost_chart",
                    "title": "📊 Department-wise Sales vs Food Cost",
                    "subtitle": "Comparison of sales revenue and food cost by department",
                    "type": "chart",
                    "size": "full",
                    "data": {
                        "chart_type": "bar",
                        "labels": dept_names,
                        "datasets": [
                            {
                                "label": "Sales (₹)",
                                "data": dept_sales,
                                "backgroundColor": "#2E7D32",
                                "borderColor": "#1B5E20",
                                "borderWidth": 1,
                                "yAxisID": "y"
                            },
                            {
                                "label": "Food Cost (₹)",
                                "data": dept_consumption,
                                "backgroundColor": "#D84315",
                                "borderColor": "#BF360C",
                                "borderWidth": 1,
                                "yAxisID": "y"
                            }
                        ],
                        "options": {
                            "responsive": True,
                            "scales": {
                                "y": {
                                    "type": "linear",
                                    "display": True,
                                    "position": "left",
                                    "title": {"display": True, "text": "Amount (₹)"}
                                }
                            },
                            "plugins": {
                                "legend": {"display": True, "position": "top"},
                                "title": {"display": True, "text": "Sales vs Food Cost by Department"}
                            }
                        }
                    }
                })

                # Add Food Cost Percentage Chart
                charts.append({
                    "id": "food_cost_percentage_chart",
                    "title": "📈 Food Cost Percentage by Department",
                    "subtitle": "Food cost as percentage of sales revenue",
                    "type": "chart",
                    "size": "half",
                    "data": {
                        "chart_type": "doughnut",
                        "labels": dept_names,
                        "datasets": [{
                            "label": "Food Cost %",
                            "data": dept_cost_pct,
                            "backgroundColor": [
                                "#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0",
                                "#9966FF", "#FF9F40", "#FF6384", "#C9CBCF"
                            ][:len(dept_names)],
                            "borderWidth": 2,
                            "borderColor": "#fff"
                        }],
                        "options": {
                            "responsive": True,
                            "plugins": {
                                "legend": {"display": True, "position": "right"},
                                "title": {"display": True, "text": "Food Cost % Distribution"}
                            }
                        }
                    }
                })

            # Add the detailed Food Cost Analysis table
            food_cost_analysis = generate_food_cost_analysis_table(
                reconciliation_data, processed_sales, category_to_department
            )
            charts.append(food_cost_analysis)

        # ===== RECONCILIATION TABLES SECTION =====

        # Create Enhanced Reconciliation Table
        # Build comprehensive table data for the reconciliation report
        table_data = []
        table_headers = [
            "Expense Head/Item",
            "Opening Stock - Store (₹)", "Opening Stock - Workareas (₹)", "Opening Stock - Total (₹)",
            "Transfer In/Out (Store) (₹)", "Transfer In/Out (Workareas) (₹)",
            "Closing Stock - Store (₹)", "Closing Stock - Workareas (₹)", "Closing Stock - Total (₹)",
            "Consumption (₹)"
        ]

        # Process each category with enhanced formatting
        for category, category_data in reconciliation_data.items():
            # Add category header row with enhanced styling
            category_totals = category_data['totals']





            table_data.append({
                "Expense Head/Item": f"{category}",
                "Opening Stock - Store (₹)": format_indian_currency(category_totals['opening_stock_store']) if category_totals['opening_stock_store'] > 0 else "-",
                "Opening Stock - Workareas (₹)": format_indian_currency(category_totals['opening_stock_kitchen']) if category_totals['opening_stock_kitchen'] > 0 else "-",
                "Opening Stock - Total (₹)": format_indian_currency(category_totals['opening_stock_total']) if category_totals['opening_stock_total'] > 0 else "-",
                "Transfer In/Out (Store) (₹)": format_indian_currency(category_totals['transfer_in_out_store']) if abs(category_totals['transfer_in_out_store']) > 0.01 else "-",
                "Transfer In/Out (Workareas) (₹)": format_indian_currency(category_totals['transfer_in_out_kitchen']) if abs(category_totals['transfer_in_out_kitchen']) > 0.01 else "-",
                "Closing Stock - Store (₹)": format_indian_currency(category_totals['closing_stock_store']) if category_totals['closing_stock_store'] > 0 else "-",
                "Closing Stock - Workareas (₹)": format_indian_currency(category_totals['closing_stock_kitchen']) if category_totals['closing_stock_kitchen'] > 0 else "-",
                "Closing Stock - Total (₹)": format_indian_currency(category_totals['closing_stock_total']) if category_totals['closing_stock_total'] > 0 else "-",
                "Consumption (₹)": format_indian_currency(category_totals['consumption']) if category_totals['consumption'] > 0 else "-",
                "_isCategory": True  # Flag for styling
            })



            # Add subcategory rows (only if they have meaningful data)
            for subcategory, subcat_data in category_data['subcategories'].items():
                # Check only numeric values, skip dictionaries like transfer_details
                numeric_keys = ['opening_stock_store', 'opening_stock_kitchen', 'opening_stock_total',
                               'purchase', 'transfer_in_out', 'closing_stock_store', 'closing_stock_kitchen',
                               'closing_stock_total', 'consumption']
                if any(abs(subcat_data.get(key, 0)) > 0.01 for key in numeric_keys):

                    table_data.append({
                        "Expense Head/Item": f"  {subcategory}",
                        "Opening Stock - Store (₹)": format_indian_currency(subcat_data['opening_stock_store']) if abs(subcat_data['opening_stock_store']) > 0.01 else "-",
                        "Opening Stock - Workareas (₹)": format_indian_currency(subcat_data['opening_stock_kitchen']) if abs(subcat_data['opening_stock_kitchen']) > 0.01 else "-",
                        "Opening Stock - Total (₹)": format_indian_currency(subcat_data['opening_stock_total']) if abs(subcat_data['opening_stock_total']) > 0.01 else "-",
                        "Transfer In/Out (Store) (₹)": format_indian_currency(subcat_data['transfer_in_out_store']) if abs(subcat_data['transfer_in_out_store']) > 0.01 else "-",
                        "Transfer In/Out (Workareas) (₹)": format_indian_currency(subcat_data['transfer_in_out_kitchen']) if abs(subcat_data['transfer_in_out_kitchen']) > 0.01 else "-",
                        "Closing Stock - Store (₹)": format_indian_currency(subcat_data['closing_stock_store']) if abs(subcat_data['closing_stock_store']) > 0.01 else "-",
                        "Closing Stock - Workareas (₹)": format_indian_currency(subcat_data['closing_stock_kitchen']) if abs(subcat_data['closing_stock_kitchen']) > 0.01 else "-",
                        "Closing Stock - Total (₹)": format_indian_currency(subcat_data['closing_stock_total']) if abs(subcat_data['closing_stock_total']) > 0.01 else "-",
                        "Consumption (₹)": format_indian_currency(subcat_data['consumption']) if abs(subcat_data['consumption']) > 0.01 else "-",
                        "_isCategory": False,
                        "_isSubcategory": True
                    })

        # Add Grand Total row with enhanced styling
        table_data.append({
            "Expense Head/Item": "GRAND TOTAL",
            "Opening Stock - Store (₹)": format_indian_currency(grand_totals['opening_stock_store']),
            "Opening Stock - Workareas (₹)": format_indian_currency(grand_totals['opening_stock_kitchen']),
            "Opening Stock - Total (₹)": format_indian_currency(grand_totals['opening_stock_total']),
            "Transfer In/Out (Store) (₹)": format_indian_currency(grand_totals['transfer_in_out_store']),
            "Transfer In/Out (Workareas) (₹)": format_indian_currency(grand_totals['transfer_in_out_kitchen']),
            "Closing Stock - Store (₹)": format_indian_currency(grand_totals['closing_stock_store']),
            "Closing Stock - Workareas (₹)": format_indian_currency(grand_totals['closing_stock_kitchen']),
            "Closing Stock - Total (₹)": format_indian_currency(grand_totals['closing_stock_total']),
            "Consumption (₹)": format_indian_currency(grand_totals['consumption']),
            "_isGrandTotal": True
        })

        # Create the main reconciliation table
        charts.append({
            "id": "reconciliation_table_main",
            "title": "Reconciliation: Opening + Store Transfer + Workareas Transfer - Closing = Consumption",
            "subtitle": f"Analysis from {len(store_df)} store records and {len(consumption_df)} consumption records",
            "type": "table",
            "size": "full",
            "data": {
                "headers": table_headers,
                "rows": table_data
            },
            "options": {
                "responsive": True,
                "pageSize": 100,
                "searchable": True,
                "sortable": True,
                "exportable": True,
                "showPagination": True,
                "showSearch": True,
                "showExport": True,
                "styling": {
                    "headerBackground": "#ff9800",
                    "headerColor": "#ffffff",
                    "alternateRowColor": "#fafafa",
                    "borderColor": "#e0e0e0",
                    "categoryRowBackground": "#fff8f0",
                    "categoryRowColor": "#e65100",
                    "subcategoryRowBackground": "#ffffff",
                    "grandTotalBackground": "#ff9800",
                    "grandTotalColor": "#ffffff",
                    "fontSize": "11px",
                    "fontFamily": "Inter, Segoe UI, Tahoma, Geneva, Verdana, sans-serif"
                }
            }
        })

        # Create detailed transfer breakdown table
        transfer_breakdown_data = []
        transfer_headers = [
            "Category/Subcategory",
            "WorkArea Transfer In (₹)",
            "Total Inward (₹)",
            "WorkArea Transfer Out (₹)",
            "Return To Store Out (₹)",
            "Total Outward (₹)",
            "Spoilage/Adjustments (₹)",
            "Cross-Category Indents (₹)",
            "Net Transfer (₹)"
        ]

        for category, category_data in reconciliation_data.items():
            # Calculate category totals for transfer breakdown
            cat_indent = cat_transfer_in = cat_transfer_out = cat_return_store = cat_spoilage = cat_cross_category_indents = 0

            for subcat_data in category_data['subcategories'].values():
                if 'transfer_details' in subcat_data:
                    details = subcat_data['transfer_details']
                    cat_indent += details.get('indent', 0)
                    cat_transfer_in += details.get('transfer_in', 0)
                    cat_transfer_out += details.get('transfer_out', 0)
                    cat_return_store += details.get('return_to_store', 0)
                    cat_spoilage += details.get('spoilage_adjustments', 0)
                    cat_cross_category_indents += details.get('cross_category_indents', 0)

            cat_total_inward = cat_transfer_in  # INDENT EXCLUDED FROM KITCHEN TRANSFERS
            cat_total_outward = cat_transfer_out + cat_return_store
            cat_net_transfer = cat_total_inward - cat_total_outward + cat_spoilage + cat_cross_category_indents

            # Add category row for all categories (to match main reconciliation table)
            # Show all categories that appear in the main reconciliation table
            transfer_breakdown_data.append({
                    "Category/Subcategory": f"{category}",
                    "WorkArea Transfer In (₹)": format_indian_currency(cat_transfer_in) if cat_transfer_in > 0 else "-",
                    "Total Inward (₹)": format_indian_currency(cat_total_inward) if cat_total_inward > 0 else "-",
                    "WorkArea Transfer Out (₹)": format_indian_currency(cat_transfer_out) if cat_transfer_out > 0 else "-",
                    "Return To Store Out (₹)": format_indian_currency(cat_return_store) if cat_return_store > 0 else "-",
                    "Total Outward (₹)": format_indian_currency(cat_total_outward) if cat_total_outward > 0 else "-",
                    "Spoilage/Adjustments (₹)": format_indian_currency(cat_spoilage) if abs(cat_spoilage) > 0.01 else "-",
                    "Cross-Category Indents (₹)": format_indian_currency(cat_cross_category_indents) if abs(cat_cross_category_indents) > 0.01 else "-",
                    "Net Transfer (₹)": format_indian_currency(cat_net_transfer),
                    "_isCategory": True
                })

            # Add subcategory rows with transfer details
            for subcategory, subcat_data in category_data['subcategories'].items():
                # All subcategories now have transfer_details initialized
                details = subcat_data['transfer_details']
                transfer_in = details.get('transfer_in', 0)
                transfer_out = details.get('transfer_out', 0)
                return_store = details.get('return_to_store', 0)
                spoilage = details.get('spoilage_adjustments', 0)
                cross_category_indents = details.get('cross_category_indents', 0)

                total_inward = transfer_in  # INDENT EXCLUDED FROM KITCHEN TRANSFERS
                total_outward = transfer_out + return_store
                net_transfer = total_inward - total_outward + spoilage + cross_category_indents

                # Show all subcategories to match main reconciliation table
                transfer_breakdown_data.append({
                    "Category/Subcategory": f"  {subcategory}",
                    "WorkArea Transfer In (₹)": format_indian_currency(transfer_in) if transfer_in > 0 else "-",
                    "Total Inward (₹)": format_indian_currency(total_inward) if total_inward > 0 else "-",
                    "WorkArea Transfer Out (₹)": format_indian_currency(transfer_out) if transfer_out > 0 else "-",
                    "Return To Store Out (₹)": format_indian_currency(return_store) if return_store > 0 else "-",
                    "Total Outward (₹)": format_indian_currency(total_outward) if total_outward > 0 else "-",
                    "Spoilage/Adjustments (₹)": format_indian_currency(spoilage) if abs(spoilage) > 0.01 else "-",
                    "Cross-Category Indents (₹)": format_indian_currency(cross_category_indents) if abs(cross_category_indents) > 0.01 else "-",
                    "Net Transfer (₹)": format_indian_currency(net_transfer) if abs(net_transfer) > 0.01 else "-",
                    "_isSubcategory": True
                })

        # Add transfer breakdown table if there's data
        if transfer_breakdown_data:
            charts.append({
                "id": "transfer_breakdown_table",
                "title": "📋 Transfer In/Out (Workareas): Transfer In - Transfer Out - Return To Store + Spoilage + Cross-Category Indents",
                "subtitle": "Detailed workarea transfer breakdown with cross-category indent reconciliation",
                "type": "table",
                "size": "full",
                "data": {
                    "headers": transfer_headers,
                    "rows": transfer_breakdown_data
                },
                "options": {
                    "responsive": True,
                    "pageSize": 50,
                    "searchable": True,
                    "sortable": True,
                    "exportable": True,
                    "showPagination": True,
                    "showSearch": True,
                    "showExport": True,
                    "styling": {
                        "headerBackground": "#2196f3",
                        "headerColor": "#ffffff",
                        "alternateRowColor": "#f8f9fa",
                        "borderColor": "#e0e0e0",
                        "categoryRowBackground": "#e3f2fd",
                        "categoryRowColor": "#1976d2",
                        "subcategoryRowBackground": "#ffffff",
                        "fontSize": "11px",
                        "fontFamily": "Inter, Segoe UI, Tahoma, Geneva, Verdana, sans-serif"
                    }
                }
            })

        # Create detailed store transfer breakdown table
        store_breakdown_data = []
        store_headers = [
            "Category/Subcategory",
            "Purchase (₹)",
            "IBT In (₹)",
            "Total Inward (₹)",
            "IBT Out (₹)",
            "Return Qty (₹)",
            "Total Outward (₹)",
            "Spoilage (₹)",
            "Net Transfer (₹)"
        ]

        for category, category_data in reconciliation_data.items():
            # Calculate category-level store transfer totals
            cat_purchase = 0
            cat_ibt_in = 0
            cat_ibt_out = 0
            cat_return_qty = 0
            cat_spoilage = 0
            cat_indent = 0

            for subcat_data in category_data['subcategories'].values():
                if 'store_transfer_details' in subcat_data:
                    details = subcat_data['store_transfer_details']
                    cat_purchase += details.get('purchase', 0)
                    cat_ibt_in += details.get('ibt_in', 0)
                    cat_ibt_out += details.get('ibt_out', 0)
                    cat_return_qty += details.get('return_qty', 0)
                    cat_spoilage += details.get('spoilage', 0)
                    cat_indent += details.get('indent', 0)

            cat_total_inward = cat_purchase + cat_ibt_in
            cat_total_outward = cat_ibt_out + abs(cat_return_qty)  # Return qty is typically negative, so take absolute
            cat_net_transfer = cat_total_inward - cat_total_outward + cat_spoilage

            # Add category row if there's any store transfer activity
            if abs(cat_net_transfer) > 0.01:
                store_breakdown_data.append({
                    "Category/Subcategory": f"{category}",
                    "Purchase (₹)": format_indian_currency(cat_purchase) if cat_purchase > 0 else "-",
                    "IBT In (₹)": format_indian_currency(cat_ibt_in) if cat_ibt_in > 0 else "-",
                    "Total Inward (₹)": format_indian_currency(cat_total_inward) if cat_total_inward > 0 else "-",
                    "IBT Out (₹)": format_indian_currency(cat_ibt_out) if cat_ibt_out > 0 else "-",
                    "Return Qty (₹)": format_indian_currency(abs(cat_return_qty)) if abs(cat_return_qty) > 0 else "-",
                    "Total Outward (₹)": format_indian_currency(cat_total_outward) if cat_total_outward > 0 else "-",
                    "Spoilage (₹)": format_indian_currency(cat_spoilage) if abs(cat_spoilage) > 0.01 else "-",
                    "Net Transfer (₹)": format_indian_currency(cat_net_transfer),
                    "_isCategory": True
                })

                # Add subcategory rows with store transfer details
                for subcategory, subcat_data in category_data['subcategories'].items():
                    if 'store_transfer_details' in subcat_data:
                        details = subcat_data['store_transfer_details']
                        purchase = details.get('purchase', 0)
                        ibt_in = details.get('ibt_in', 0)
                        ibt_out = details.get('ibt_out', 0)
                        return_qty = details.get('return_qty', 0)
                        spoilage = details.get('spoilage', 0)

                        total_inward = purchase + ibt_in
                        total_outward = ibt_out + abs(return_qty)
                        net_transfer = total_inward - total_outward + spoilage

                        # Only add subcategory if it has meaningful transfer activity
                        if abs(net_transfer) > 0.01:
                            store_breakdown_data.append({
                                "Category/Subcategory": f"  {subcategory}",
                                "Purchase (₹)": format_indian_currency(purchase) if purchase > 0 else "-",
                                "IBT In (₹)": format_indian_currency(ibt_in) if ibt_in > 0 else "-",
                                "Total Inward (₹)": format_indian_currency(total_inward) if total_inward > 0 else "-",
                                "IBT Out (₹)": format_indian_currency(ibt_out) if ibt_out > 0 else "-",
                                "Return Qty (₹)": format_indian_currency(abs(return_qty)) if abs(return_qty) > 0 else "-",
                                "Total Outward (₹)": format_indian_currency(total_outward) if total_outward > 0 else "-",
                                "Spoilage (₹)": format_indian_currency(spoilage) if abs(spoilage) > 0.01 else "-",
                                "Net Transfer (₹)": format_indian_currency(net_transfer),
                                "_isSubcategory": True
                            })

        # Add the store transfer breakdown table
        if store_breakdown_data:
            charts.append({
                "id": "store_transfer_breakdown",
                "title": "Transfer In/Out (Store): Purchase + IBT In - IBT Out - Return Qty + Spoilage + Indent",
                "subtitle": f"Store-level transfer analysis showing purchase, IBT, returns, indents and spoilage",
                "type": "table",
                "size": "full",
                "data": {
                    "headers": store_headers,
                    "rows": store_breakdown_data
                },
                "options": {
                    "responsive": True,
                    "pageSize": 50,
                    "searchable": True,
                    "sortable": True,
                    "exportable": True,
                    "showPagination": True,
                    "showSearch": True,
                    "showExport": True,
                    "styling": {
                        "headerBackground": "#ff9800",
                        "headerColor": "#ffffff",
                        "alternateRowColor": "#fff3e0",
                        "borderColor": "#e0e0e0",
                        "categoryRowBackground": "#ffe0b2",
                        "categoryRowColor": "#f57c00",
                        "subcategoryRowBackground": "#ffffff",
                        "fontSize": "11px",
                        "fontFamily": "Inter, Segoe UI, Tahoma, Geneva, Verdana, sans-serif"
                    }
                }
            })

        return {
            "success": True,
            "charts": charts,
            "summary_items": []  # No summary items as requested - only table data
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "charts": [],
            "summary_items": []
        }


